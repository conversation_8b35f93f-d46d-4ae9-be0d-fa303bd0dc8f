/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file interrupt_config.c
 * @brief 
 * 
 */


#include <stddef.h>
#include "interrupt_config.h"


void INT_SYS_ConfigInit(void)
{
    INT_SYS_DisableIRQGlobal();
    /* LINFlexD0_IRQn(31) LINFlexD0_IRQHandler*/
    INT_SYS_SetPriority(LINFlexD0_IRQn,2);
    INT_SYS_EnableIRQ(LINFlexD0_IRQn);
    /* pTMR0_Ch0_IRQn(48) pTMR0_Ch0_IRQHandler*/
    INT_SYS_SetPriority(pTMR0_Ch0_IRQn,2);
    INT_SYS_EnableIRQ(pTMR0_Ch0_IRQn);
    /* lpTMR0_IRQn(58) lpTMR0_IRQHandler*/
    INT_SYS_SetPriority(lpTMR0_IRQn,2);
    INT_SYS_EnableIRQ(lpTMR0_IRQn);
    INT_SYS_EnableIRQGlobal();
}

