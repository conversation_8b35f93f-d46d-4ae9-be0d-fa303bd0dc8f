# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.26

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: uds_lin_bootloader
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/
# =============================================================================
# Object build statements for STATIC_LIBRARY target GENERATED_CONFIG_TARGET


#############################################
# Order-only phony target for GENERATED_CONFIG_TARGET

build cmake_object_order_depends_target_GENERATED_CONFIG_TARGET: phony || cmake_object_order_depends_target_GENERATED_SDK_TARGET

build CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/clock_config.c.o: C_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/clock_config.c || cmake_object_order_depends_target_GENERATED_CONFIG_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board\clock_config.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board

build CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/pin_mux.c.o: C_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/pin_mux.c || cmake_object_order_depends_target_GENERATED_CONFIG_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board\pin_mux.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board

build CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/lin_lib_config.c.o: C_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/lin_lib_config.c || cmake_object_order_depends_target_GENERATED_CONFIG_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board\lin_lib_config.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board

build CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/startup.S.o: ASM_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/startup.S || cmake_object_order_depends_target_GENERATED_CONFIG_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board\startup.S.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board

build CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/vector.S.o: ASM_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/vector.S || cmake_object_order_depends_target_GENERATED_CONFIG_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board\vector.S.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board

build CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/vector_table_copy.c.o: C_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/vector_table_copy.c || cmake_object_order_depends_target_GENERATED_CONFIG_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board\vector_table_copy.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board

build CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/RamInit0.S.o: ASM_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/RamInit0.S || cmake_object_order_depends_target_GENERATED_CONFIG_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board\RamInit0.S.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board

build CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/RamInit1.c.o: C_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/RamInit1.c || cmake_object_order_depends_target_GENERATED_CONFIG_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board\RamInit1.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board

build CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/RamInit2.c.o: C_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/RamInit2.c || cmake_object_order_depends_target_GENERATED_CONFIG_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board\RamInit2.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board

build CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/interrupt_config.c.o: C_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/interrupt_config.c || cmake_object_order_depends_target_GENERATED_CONFIG_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board\interrupt_config.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board

build CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/dma_config.c.o: C_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/dma_config.c || cmake_object_order_depends_target_GENERATED_CONFIG_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board\dma_config.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board

build CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/linflexd_lin_config.c.o: C_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/linflexd_lin_config.c || cmake_object_order_depends_target_GENERATED_CONFIG_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board\linflexd_lin_config.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board

build CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/ptmr_config.c.o: C_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/ptmr_config.c || cmake_object_order_depends_target_GENERATED_CONFIG_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board\ptmr_config.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board

build CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/lptmr_config.c.o: C_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/lptmr_config.c || cmake_object_order_depends_target_GENERATED_CONFIG_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board\lptmr_config.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board

build CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/crc_config.c.o: C_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/crc_config.c || cmake_object_order_depends_target_GENERATED_CONFIG_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board\crc_config.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board

build CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/hcu_config.c.o: C_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/hcu_config.c || cmake_object_order_depends_target_GENERATED_CONFIG_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board\hcu_config.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board

build CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/flash_config.c.o: C_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/flash_config.c || cmake_object_order_depends_target_GENERATED_CONFIG_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board\flash_config.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board

build CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/uds_config.c.o: C_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/uds_config.c || cmake_object_order_depends_target_GENERATED_CONFIG_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board\uds_config.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir\board


# =============================================================================
# Link build statements for STATIC_LIBRARY target GENERATED_CONFIG_TARGET


#############################################
# Link the static library libGENERATED_CONFIG_TARGET.a

build libGENERATED_CONFIG_TARGET.a: C_STATIC_LIBRARY_LINKER__GENERATED_CONFIG_TARGET_Debug CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/clock_config.c.o CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/pin_mux.c.o CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/lin_lib_config.c.o CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/startup.S.o CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/vector.S.o CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/vector_table_copy.c.o CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/RamInit0.S.o CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/RamInit1.c.o CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/RamInit2.c.o CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/interrupt_config.c.o CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/dma_config.c.o CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/linflexd_lin_config.c.o CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/ptmr_config.c.o CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/lptmr_config.c.o CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/crc_config.c.o CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/hcu_config.c.o CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/flash_config.c.o CMakeFiles/GENERATED_CONFIG_TARGET.dir/board/uds_config.c.o || libGENERATED_SDK_TARGET.a
  LANGUAGE_COMPILE_FLAGS = -mcpu=cortex-m33 -g
  OBJECT_DIR = CMakeFiles\GENERATED_CONFIG_TARGET.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = libGENERATED_CONFIG_TARGET.a
  TARGET_PDB = GENERATED_CONFIG_TARGET.a.dbg

# =============================================================================
# Object build statements for STATIC_LIBRARY target GENERATED_SDK_TARGET


#############################################
# Order-only phony target for GENERATED_SDK_TARGET

build cmake_object_order_depends_target_GENERATED_SDK_TARGET: phony || CMakeFiles/GENERATED_SDK_TARGET.dir

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/clock/YTM32B1Mx/clock_YTM32B1Mx.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx/clock_YTM32B1Mx.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\clock\YTM32B1Mx\clock_YTM32B1Mx.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\clock\YTM32B1Mx

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/pins/pins_driver.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins/pins_driver.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\pins\pins_driver.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\pins

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/pins/pins_port_hw_access.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins/pins_port_hw_access.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\pins\pins_port_hw_access.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\pins

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/interrupt/interrupt_manager.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/interrupt/interrupt_manager.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\interrupt\interrupt_manager.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\interrupt

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/dma/dma_driver.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma/dma_driver.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\dma\dma_driver.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\dma

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/dma/dma_hw_access.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma/dma_hw_access.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\dma\dma_hw_access.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\dma

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/dma/dma_irq.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma/dma_irq.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\dma\dma_irq.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\dma

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/linflexd/linflexd_lin_driver.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd/linflexd_lin_driver.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\linflexd\linflexd_lin_driver.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\linflexd

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/linflexd/linflexd_lin_irq.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd/linflexd_lin_irq.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\linflexd\linflexd_lin_irq.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\linflexd

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/ptmr/ptmr_driver.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr/ptmr_driver.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\ptmr\ptmr_driver.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\ptmr

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/lptmr/lptmr_driver.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr/lptmr_driver.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\lptmr\lptmr_driver.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\lptmr

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/lptmr/lptmr_hw_access.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr/lptmr_hw_access.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\lptmr\lptmr_hw_access.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\lptmr

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/crc/crc_driver.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc/crc_driver.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\crc\crc_driver.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\crc

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/crc/crc_hw_access.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc/crc_hw_access.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\crc\crc_hw_access.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\crc

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/hcu/hcu_driver.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu/hcu_driver.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\hcu\hcu_driver.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\hcu

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/hcu/hcu_irq.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu/hcu_irq.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\hcu\hcu_irq.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\hcu

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/trng/trng_driver.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng/trng_driver.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\trng\trng_driver.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\trng

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/trng/trng_hw_access.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng/trng_hw_access.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\trng\trng_hw_access.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\trng

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/flash/flash_driver.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash/flash_driver.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\flash\flash_driver.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\drivers\src\flash

build CMakeFiles/GENERATED_SDK_TARGET.dir/platform/devices/YTM32B1MD1/startup/system_YTM32B1MD1.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup/system_YTM32B1MD1.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\devices\YTM32B1MD1\startup\system_YTM32B1MD1.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\platform\devices\YTM32B1MD1\startup

build CMakeFiles/GENERATED_SDK_TARGET.dir/middleware/lin_stack/src/lin_core.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/src/lin_core.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\middleware\lin_stack\src\lin_core.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\middleware\lin_stack\src

build CMakeFiles/GENERATED_SDK_TARGET.dir/middleware/lin_stack/src/lin_tp.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/src/lin_tp.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\middleware\lin_stack\src\lin_tp.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\middleware\lin_stack\src

build CMakeFiles/GENERATED_SDK_TARGET.dir/middleware/uds/src/uds_ip.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/src/uds_ip.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\middleware\uds\src\uds_ip.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\middleware\uds\src

build CMakeFiles/GENERATED_SDK_TARGET.dir/middleware/uds/src/uds.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/src/uds.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\middleware\uds\src\uds.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\middleware\uds\src

build CMakeFiles/GENERATED_SDK_TARGET.dir/rtos/osif/osif_baremetal.c.o: C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif/osif_baremetal.c || cmake_object_order_depends_target_GENERATED_SDK_TARGET
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\GENERATED_SDK_TARGET.dir\rtos\osif\osif_baremetal.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  OBJECT_FILE_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir\rtos\osif


# =============================================================================
# Link build statements for STATIC_LIBRARY target GENERATED_SDK_TARGET


#############################################
# Link the static library libGENERATED_SDK_TARGET.a

build libGENERATED_SDK_TARGET.a: C_STATIC_LIBRARY_LINKER__GENERATED_SDK_TARGET_Debug CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/clock/YTM32B1Mx/clock_YTM32B1Mx.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/pins/pins_driver.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/pins/pins_port_hw_access.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/interrupt/interrupt_manager.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/dma/dma_driver.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/dma/dma_hw_access.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/dma/dma_irq.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/linflexd/linflexd_lin_driver.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/linflexd/linflexd_lin_irq.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/ptmr/ptmr_driver.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/lptmr/lptmr_driver.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/lptmr/lptmr_hw_access.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/crc/crc_driver.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/crc/crc_hw_access.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/hcu/hcu_driver.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/hcu/hcu_irq.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/trng/trng_driver.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/trng/trng_hw_access.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/platform/drivers/src/flash/flash_driver.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/platform/devices/YTM32B1MD1/startup/system_YTM32B1MD1.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/middleware/lin_stack/src/lin_core.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/middleware/lin_stack/src/lin_tp.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/middleware/uds/src/uds_ip.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/middleware/uds/src/uds.c.o CMakeFiles/GENERATED_SDK_TARGET.dir/rtos/osif/osif_baremetal.c.o
  LANGUAGE_COMPILE_FLAGS = -mcpu=cortex-m33 -g
  OBJECT_DIR = CMakeFiles\GENERATED_SDK_TARGET.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = libGENERATED_SDK_TARGET.a
  TARGET_PDB = GENERATED_SDK_TARGET.a.dbg

# =============================================================================
# Object build statements for EXECUTABLE target uds_lin_bootloader.elf


#############################################
# Order-only phony target for uds_lin_bootloader.elf

build cmake_object_order_depends_target_uds_lin_bootloader.elf: phony || cmake_object_order_depends_target_GENERATED_CONFIG_TARGET

build CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o: C_COMPILER__uds_lin_bootloader.2eelf_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/app/main.c || cmake_object_order_depends_target_uds_lin_bootloader.elf
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\uds_lin_bootloader.elf.dir\app\main.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/app -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\uds_lin_bootloader.elf.dir
  OBJECT_FILE_DIR = CMakeFiles\uds_lin_bootloader.elf.dir\app

build CMakeFiles/uds_lin_bootloader.elf.dir/app/GenerateKeyExImpl.c.o: C_COMPILER__uds_lin_bootloader.2eelf_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/app/GenerateKeyExImpl.c || cmake_object_order_depends_target_uds_lin_bootloader.elf
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\uds_lin_bootloader.elf.dir\app\GenerateKeyExImpl.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/app -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\uds_lin_bootloader.elf.dir
  OBJECT_FILE_DIR = CMakeFiles\uds_lin_bootloader.elf.dir\app

build CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o: C_COMPILER__uds_lin_bootloader.2eelf_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/app/Uds_Service.c || cmake_object_order_depends_target_uds_lin_bootloader.elf
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\uds_lin_bootloader.elf.dir\app\Uds_Service.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/app -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\uds_lin_bootloader.elf.dir
  OBJECT_FILE_DIR = CMakeFiles\uds_lin_bootloader.elf.dir\app

build CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o: C_COMPILER__uds_lin_bootloader.2eelf_unscanned_Debug D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/app/boot_jump.c || cmake_object_order_depends_target_uds_lin_bootloader.elf
  DEFINES = -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1
  DEP_FILE = CMakeFiles\uds_lin_bootloader.elf.dir\app\boot_jump.c.o.d
  FLAGS = -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always
  INCLUDES = -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/app -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif
  OBJECT_DIR = CMakeFiles\uds_lin_bootloader.elf.dir
  OBJECT_FILE_DIR = CMakeFiles\uds_lin_bootloader.elf.dir\app


# =============================================================================
# Link build statements for EXECUTABLE target uds_lin_bootloader.elf


#############################################
# Link the executable uds_lin_bootloader.elf

build uds_lin_bootloader.elf: C_EXECUTABLE_LINKER__uds_lin_bootloader.2eelf_Debug CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o CMakeFiles/uds_lin_bootloader.elf.dir/app/GenerateKeyExImpl.c.o CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o | libGENERATED_CONFIG_TARGET.a libGENERATED_SDK_TARGET.a libGENERATED_CONFIG_TARGET.a libGENERATED_SDK_TARGET.a || libGENERATED_CONFIG_TARGET.a
  FLAGS = -mcpu=cortex-m33 -g
  LINK_FLAGS = -mcpu=cortex-m33    -Wl,--start-group -mcpu=cortex-m33 -specs=nosys.specs
  LINK_LIBRARIES = -Wl,--whole-archive  libGENERATED_CONFIG_TARGET.a  libGENERATED_SDK_TARGET.a  -Wl,--no-whole-archive  -Xlinker --gc-sections  -nostartfiles  --entry=Reset_Handler  -TD:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/yt_linker.ld  -Wl,-Map=uds_lin_bootloader.map  -Wl,--end-group  libGENERATED_CONFIG_TARGET.a  libGENERATED_SDK_TARGET.a
  OBJECT_DIR = CMakeFiles\uds_lin_bootloader.elf.dir
  POST_BUILD = cmd.exe /C "cd /D D:\project\uds_lin_fbl_md14_release_20250509\uds_lin_bootloader\build && C:\Users\<USER>\AppData\Roaming\yt_config_tool\gcc-arm-none-eabi-10.3-2021.10\bin\arm-none-eabi-size.exe --format=berkeley uds_lin_bootloader.elf && cd /D D:\project\uds_lin_fbl_md14_release_20250509\uds_lin_bootloader\build && C:\Users\<USER>\AppData\Roaming\yt_config_tool\gcc-arm-none-eabi-10.3-2021.10\bin\arm-none-eabi-objcopy.exe -F elf32-littlearm -O binary D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/uds_lin_bootloader.elf D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/uds_lin_bootloader.bin && cd /D D:\project\uds_lin_fbl_md14_release_20250509\uds_lin_bootloader\build && C:\Users\<USER>\AppData\Roaming\yt_config_tool\gcc-arm-none-eabi-10.3-2021.10\bin\arm-none-eabi-objcopy.exe -F elf32-littlearm -O ihex D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/uds_lin_bootloader.elf D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/uds_lin_bootloader.hex && cd /D D:\project\uds_lin_fbl_md14_release_20250509\uds_lin_bootloader\build && C:\Users\<USER>\AppData\Roaming\yt_config_tool\gcc-arm-none-eabi-10.3-2021.10\bin\arm-none-eabi-objcopy.exe -F elf32-littlearm -O srec D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/uds_lin_bootloader.elf D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/uds_lin_bootloader.s19"
  PRE_LINK = cd .
  TARGET_FILE = uds_lin_bootloader.elf
  TARGET_PDB = uds_lin_bootloader.elf.dbg


#############################################
# Utility command for genbin

build genbin: phony CMakeFiles/genbin uds_lin_bootloader.elf


#############################################
# Utility command for genhex

build genhex: phony CMakeFiles/genhex uds_lin_bootloader.elf


#############################################
# Utility command for gens19

build gens19: phony CMakeFiles/gens19 uds_lin_bootloader.elf


#############################################
# Utility command for genlist

build genlist: phony CMakeFiles/genlist uds_lin_bootloader.elf


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\project\uds_lin_fbl_md14_release_20250509\uds_lin_bootloader\build && C:\Users\<USER>\AppData\Roaming\yt_config_tool\cmake-3.26.4-windows-x86_64\bin\cmake-gui.exe -SD:\project\uds_lin_fbl_md14_release_20250509\uds_lin_bootloader -BD:\project\uds_lin_fbl_md14_release_20250509\uds_lin_bootloader\build"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\project\uds_lin_fbl_md14_release_20250509\uds_lin_bootloader\build && C:\Users\<USER>\AppData\Roaming\yt_config_tool\cmake-3.26.4-windows-x86_64\bin\cmake.exe --regenerate-during-build -SD:\project\uds_lin_fbl_md14_release_20250509\uds_lin_bootloader -BD:\project\uds_lin_fbl_md14_release_20250509\uds_lin_bootloader\build"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Custom command for CMakeFiles\genbin

build CMakeFiles/genbin | ${cmake_ninja_workdir}CMakeFiles/genbin: CUSTOM_COMMAND uds_lin_bootloader.elf || libGENERATED_CONFIG_TARGET.a libGENERATED_SDK_TARGET.a uds_lin_bootloader.elf
  COMMAND = cmd.exe /C "cd /D D:\project\uds_lin_fbl_md14_release_20250509\uds_lin_bootloader\build && C:\Users\<USER>\AppData\Roaming\yt_config_tool\gcc-arm-none-eabi-10.3-2021.10\bin\arm-none-eabi-objcopy.exe -F elf32-littlearm -O binary D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/uds_lin_bootloader.elf D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/uds_lin_bootloader.bin"


#############################################
# Custom command for CMakeFiles\genhex

build CMakeFiles/genhex | ${cmake_ninja_workdir}CMakeFiles/genhex: CUSTOM_COMMAND uds_lin_bootloader.elf || libGENERATED_CONFIG_TARGET.a libGENERATED_SDK_TARGET.a uds_lin_bootloader.elf
  COMMAND = cmd.exe /C "cd /D D:\project\uds_lin_fbl_md14_release_20250509\uds_lin_bootloader\build && C:\Users\<USER>\AppData\Roaming\yt_config_tool\gcc-arm-none-eabi-10.3-2021.10\bin\arm-none-eabi-objcopy.exe -F elf32-littlearm -O ihex D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/uds_lin_bootloader.elf D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/uds_lin_bootloader.hex"


#############################################
# Custom command for CMakeFiles\gens19

build CMakeFiles/gens19 | ${cmake_ninja_workdir}CMakeFiles/gens19: CUSTOM_COMMAND uds_lin_bootloader.elf || libGENERATED_CONFIG_TARGET.a libGENERATED_SDK_TARGET.a uds_lin_bootloader.elf
  COMMAND = cmd.exe /C "cd /D D:\project\uds_lin_fbl_md14_release_20250509\uds_lin_bootloader\build && C:\Users\<USER>\AppData\Roaming\yt_config_tool\gcc-arm-none-eabi-10.3-2021.10\bin\arm-none-eabi-objcopy.exe -F elf32-littlearm -O srec D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/uds_lin_bootloader.elf D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/uds_lin_bootloader.s19"


#############################################
# Custom command for CMakeFiles\genlist

build CMakeFiles/genlist | ${cmake_ninja_workdir}CMakeFiles/genlist: CUSTOM_COMMAND uds_lin_bootloader.elf || libGENERATED_CONFIG_TARGET.a libGENERATED_SDK_TARGET.a uds_lin_bootloader.elf
  COMMAND = cmd.exe /C "cd /D D:\project\uds_lin_fbl_md14_release_20250509\uds_lin_bootloader\build && C:\Users\<USER>\AppData\Roaming\yt_config_tool\gcc-arm-none-eabi-10.3-2021.10\bin\arm-none-eabi-objdump.exe --source --all-headers --demangle --line-numbers --wide D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/uds_lin_bootloader.elf > D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/uds_lin_bootloader.lst"

# =============================================================================
# Target aliases.

build GENERATED_CONFIG_TARGET: phony libGENERATED_CONFIG_TARGET.a

build GENERATED_SDK_TARGET: phony libGENERATED_SDK_TARGET.a

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build

build all: phony libGENERATED_CONFIG_TARGET.a libGENERATED_SDK_TARGET.a uds_lin_bootloader.elf

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeASMInformation.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakePrintHelpers.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Compiler/GNU-ASM.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Compiler/GNU-C.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Compiler/GNU-CXX.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Platform/Linux-GNU-C.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Platform/Linux-GNU-CXX.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Platform/Linux-GNU.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.26.4/CMakeASMCompiler.cmake CMakeFiles/3.26.4/CMakeCCompiler.cmake CMakeFiles/3.26.4/CMakeCXXCompiler.cmake CMakeFiles/3.26.4/CMakeSystem.cmake D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMakeLists.txt D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/cmake/Toolchain/GCC.cmake D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/cmake/Toolchain/Tools.cmake D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/cmake/config.cmake D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/cmake/configCore.cmake D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/cmake/configLib.cmake D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/cmake/gcc.cmake D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/cmake/targets/GENERATED_CONFIG_TARGET.cmake D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/cmake/targets/GENERATED_SDK_TARGET.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeASMInformation.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakePrintHelpers.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Compiler/GNU-ASM.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Compiler/GNU-C.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Compiler/GNU-CXX.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Platform/Linux-GNU-C.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Platform/Linux-GNU-CXX.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Platform/Linux-GNU.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.26.4/CMakeASMCompiler.cmake CMakeFiles/3.26.4/CMakeCCompiler.cmake CMakeFiles/3.26.4/CMakeCXXCompiler.cmake CMakeFiles/3.26.4/CMakeSystem.cmake D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMakeLists.txt D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/cmake/Toolchain/GCC.cmake D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/cmake/Toolchain/Tools.cmake D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/cmake/config.cmake D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/cmake/configCore.cmake D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/cmake/configLib.cmake D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/cmake/gcc.cmake D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/cmake/targets/GENERATED_CONFIG_TARGET.cmake D$:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/cmake/targets/GENERATED_SDK_TARGET.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
