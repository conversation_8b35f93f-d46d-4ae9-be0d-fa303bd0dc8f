


Breakpoint=C:/Users/<USER>/Desktop/uds_lin_fbl_md14/uds_lin_bootloader/app/Uds_Service.c:565, State=BP_STATE_ON
Breakpoint=C:/Users/<USER>/Desktop/uds_lin_fbl_md14/uds_lin_bootloader/app/Uds_Service.c:586, State=BP_STATE_DISABLED
Breakpoint=C:/Users/<USER>/Desktop/uds_lin_fbl_md14/uds_lin_bootloader/app/Uds_Service.c:944, State=BP_STATE_ON
GraphedExpression="ECU_m_num_SysTick", DisplayFormat=DISPLAY_FORMAT_DEC, Color=#a00909
OpenDocument="Uds_Service.c", FilePath="C:/Users/<USER>/Desktop/uds_lin_fbl_md14/uds_lin_bootloader/app/Uds_Service.c", Line=143
OpenDocument="vector_table_copy.c", FilePath="C:/Users/<USER>/Desktop/uds_lin_fbl_md14/uds_lin_bootloader/board/vector_table_copy.c", Line=27
OpenDocument="main.c", FilePath="C:/Users/<USER>/Desktop/uds_lin_fbl_md14/uds_lin_bootloader/app/main.c", Line=36
OpenDocument="boot_jump.c", FilePath="C:/Users/<USER>/Desktop/uds_lin_fbl_md14/uds_lin_bootloader/app/boot_jump.c", Line=11
OpenDocument="startup.S", FilePath="C:/Users/<USER>/Desktop/uds_lin_fbl_md14/uds_lin_bootloader/board/startup.S", Line=14
OpenDocument="interrupt_manager.c", FilePath="C:/Users/<USER>/Desktop/uds_lin_fbl_md14/uds_lin_bootloader/platform/drivers/src/interrupt/interrupt_manager.c", Line=100
OpenDocument="uds.c", FilePath="C:/Users/<USER>/Desktop/uds_lin_fbl_md14/uds_lin_bootloader/middleware/uds/src/uds.c", Line=588
OpenToolbar="Debug", Floating=0, x=0, y=0
OpenWindow="Call Stack", DockArea=LEFT, x=0, y=1, w=475, h=102, TabPos=1, TopOfStack=1, FilterBarShown=0, TotalValueBarShown=0, ToolBarShown=0
OpenWindow="Registers 1", DockArea=RIGHT, x=0, y=0, w=428, h=690, TabPos=2, TopOfStack=0, FilterBarShown=0, TotalValueBarShown=0, ToolBarShown=0, FilteredItems=[], RefreshRate=1
OpenWindow="Source Files", DockArea=RIGHT, x=0, y=0, w=428, h=690, TabPos=0, TopOfStack=0, FilterBarShown=0, TotalValueBarShown=0, ToolBarShown=0
OpenWindow="Disassembly", DockArea=LEFT, x=0, y=2, w=475, h=157, FilterBarShown=0, TotalValueBarShown=0, ToolBarShown=0
OpenWindow="Memory 1", DockArea=RIGHT, x=0, y=0, w=428, h=690, TabPos=1, TopOfStack=1, FilterBarShown=0, TotalValueBarShown=0, ToolBarShown=0, EditorAddress=0xA000
OpenWindow="Local Data", DockArea=LEFT, x=0, y=1, w=475, h=102, TabPos=0, TopOfStack=0, FilterBarShown=0, TotalValueBarShown=0, ToolBarShown=0
OpenWindow="Watched Data 1", DockArea=LEFT, x=0, y=3, w=475, h=322, FilterBarShown=0, TotalValueBarShown=0, ToolBarShown=0
OpenWindow="Call Graph", DockArea=LEFT, x=0, y=0, w=475, h=106, FilterBarShown=0, TotalValueBarShown=0, ToolBarShown=0
OpenWindow="Terminal", DockArea=BOTTOM, x=1, y=0, w=1280, h=218, FilterBarShown=0, TotalValueBarShown=0, ToolBarShown=0
OpenWindow="Console", DockArea=BOTTOM, x=0, y=0, w=639, h=218, FilterBarShown=0, TotalValueBarShown=0, ToolBarShown=0
TableHeader="Call Stack", SortCol="Function", SortOrder="ASCENDING", VisibleCols=["Function";"Stack Frame";"Source";"PC";"Return Address";"Stack Used"], ColWidths=[202;181;238;104;214;100]
TableHeader="Registers 1", SortCol="Name", SortOrder="ASCENDING", VisibleCols=["Name";"Value";"Description"], ColWidths=[220;236;259]
TableHeader="Source Files", SortCol="File", SortOrder="ASCENDING", VisibleCols=["File";"Status";"Size";"#Insts";"Path"], ColWidths=[247;100;100;100;868]
TableHeader="Local Data", SortCol="Name", SortOrder="ASCENDING", VisibleCols=["Name";"Value";"Location";"Size";"Type";"Scope"], ColWidths=[119;159;101;58;145;329]
TableHeader="Call Graph", SortCol="Name", SortOrder="ASCENDING", VisibleCols=["Name";"Stack Total";"Stack Local";"Code Total";"Code Local";"Depth";"Called From"], ColWidths=[260;100;100;100;100;100;107]
TableHeader="Power Sampling", SortCol="Index", SortOrder="ASCENDING", VisibleCols=["Index";"Time";"Ch 0"], ColWidths=[100;100;100]
TableHeader="Watched Data 1", SortCol="Expression", SortOrder="ASCENDING", VisibleCols=["Expression";"Value";"Location";"Refresh"], ColWidths=[166;100;100;109]
TableHeader="RegisterSelectionDialog", SortCol="None", SortOrder="ASCENDING", VisibleCols=[], ColWidths=[]
TableHeader="TargetExceptionDialog", SortCol="Name", SortOrder="ASCENDING", VisibleCols=["Name";"Value";"Address";"Description"], ColWidths=[200;348;122;818]
WatchedExpression="eraseOffset", RefreshRate=5, DisplayFormat=DISPLAY_FORMAT_HEX, Window=Watched Data 1
WatchedExpression="udState", RefreshRate=5, DisplayFormat=DISPLAY_FORMAT_HEX, Window=Watched Data 1
WatchedExpression="ECU_m_num_SysTick", RefreshRate=1, Window=Watched Data 1