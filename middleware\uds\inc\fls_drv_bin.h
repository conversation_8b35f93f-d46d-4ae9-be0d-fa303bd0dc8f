/*
 * Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 * All rights reserved.
 *
 * YUNTU Confidential. This software is owned or controlled by YUNTU and may
 * only be used strictly in accordance with the applicable license terms. By expressly
 * accepting such terms or by downloading, installing, activating and/or otherwise
 * using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms. If you do not agree to be
 * bound by the applicable license terms, then you may not retain, install,
 * activate or otherwise use the software. The production use license in
 * Section 2.3 is expressly granted for this software.
 */
/*!
 * @file fls_drv_bin.h
 *
 */
#ifndef FLS_DRV_BIN_H
#define FLS_DRV_BIN_H

#include "stdint.h"
#include "status.h"

/* #define TEST_BY_ARRAY */


/*!
 * @brief Flash driver init function.
 */
typedef status_t (*FLASH_Init_t)(void);

/*!
 * @brief Flash driver deinit function.
 */
typedef status_t (*FLASH_Deinit_t)(void);

/*!
 * @brief Flash erase sector synchronously.
 */
typedef status_t (*FLASH_EraseSector_t)(uint32_t dest, uint32_t size);

/*!
 * @brief Flash program synchronously.
 */
typedef status_t (*FLASH_Program_t)(uint32_t dest, uint32_t size, const void * pData);

/*!
 * @brief Flash driver boot swap function.
 */
typedef status_t (*FLASH_BootSwap_t)(void);

typedef struct{
    uint32_t version;
    FLASH_Init_t init;
    FLASH_Deinit_t deinit;
    FLASH_EraseSector_t eraseSector;
    FLASH_Program_t program;    
    FLASH_BootSwap_t bootSwap;   /* Optional by chip */
}fls_drv_tbl_t;


#endif /* FLS_DRV_BIN_H */