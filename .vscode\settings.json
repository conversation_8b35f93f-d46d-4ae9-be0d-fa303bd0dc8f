{"cmake.configureArgs": ["-DCMAKE_TOOLCHAIN_FILE=${workspaceFolder}/cmake/gcc.cmake", "-DARM_CPU=cortex-m33", "-DDEVICE_NAME=YTM32B1MD1", "-DTOOLCHAIN=GCC"], "cmake.buildDirectory": "${workspaceFolder}/build", "cmake.buildToolArgs": [], "cmake.sourceDirectory": "${workspaceFolder}", "cmake.generator": "Ninja", "cmake.configureOnOpen": false, "cmake.ignoreKitEnv": true, "cmake.showSystemKits": false, "cmake.enableAutomaticKitScan": false, "cmake.enabledOutputParsers": ["cmake", "gcc", "gnuld", "msvc", "ghs", "diab", "iar"]}