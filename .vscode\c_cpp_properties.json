{"env": {"PROJ_DIR": "${workspaceFolder}"}, "configurations": [{"name": "YTM32B1MD1", "intelliSenseMode": "gcc-x64", "includePath": ["${PROJ_DIR}/board", "${PROJ_DIR}/platform/drivers/src/clock/YTM32B1Mx", "${PROJ_DIR}/platform/drivers/src/pins", "${PROJ_DIR}/platform/drivers/src/dma", "${PROJ_DIR}/platform/drivers/src/linflexd", "${PROJ_DIR}/platform/drivers/src/ptmr", "${PROJ_DIR}/platform/drivers/src/lptmr", "${PROJ_DIR}/platform/drivers/src/crc", "${PROJ_DIR}/platform/drivers/src/hcu", "${PROJ_DIR}/platform/drivers/src/trng", "${PROJ_DIR}/platform/drivers/src/flash", "${PROJ_DIR}/platform/drivers/inc", "${PROJ_DIR}/platform/devices/common", "${PROJ_DIR}/platform/devices", "${PROJ_DIR}/platform/devices/YTM32B1MD1/include", "${PROJ_DIR}/platform/devices/YTM32B1MD1/startup", "${PROJ_DIR}/middleware/lin_stack/inc", "${PROJ_DIR}/middleware/uds/inc", "${PROJ_DIR}/CMSIS/Core/Include", "${PROJ_DIR}/rtos/osif"], "defines": ["YTM32B1MD1", "CPU_YTM32B1MD1"], "cStandard": "c11", "cppStandard": "c++17", "compileCommands": ["${workspaceFolder}/build/compile_commands.json"], "browse": {"path": ["${workspaceFolder}"], "limitSymbolsToIncludedHeaders": true, "databaseFilename": ""}, "configurationProvider": "ms-vscode.cmake-tools"}], "version": 4}