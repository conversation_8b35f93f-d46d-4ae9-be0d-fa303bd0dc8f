/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file ltmr_config.c
 * @brief 
 * 
 */


#include "lptmr_config.h"

const lptmr_config_t LPTMR_Config={
    .dmaRequest=false,
    .interruptEnable=true,
    .freeRun=false,
    .workMode=lpTMR_WORKMODE_TIMER,
    .prescaler=lpTMR_PRESCALE_2,
    .bypassPrescaler=false,
    .compareValue=12000,
    .counterUnits=lpTMR_COUNTER_UNITS_TICKS,
    .pinSelect=lpTMR_PINSELECT_TMU,
    .pinPolarity=lpTMR_PINPOLARITY_RISING,
    .clockSource=lpTMR_CLOCK_SOURCE_IPC,
};

