set(CMAKE_HOST_SYSTEM "Windows-10.0.22631")
set(CMAKE_HOST_SYSTEM_NAME "Windows")
set(CMAKE_HOST_SYSTEM_VERSION "10.0.22631")
set(CMAKE_HOST_SYSTEM_PROCESSOR "AMD64")

include("D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/cmake/gcc.cmake")

set(CMAKE_SYSTEM "Linux")
set(CMAKE_SYSTEM_NAME "Linux")
set(CMAKE_SYSTEM_VERSION "")
set(CMAKE_SYSTEM_PROCESSOR "arm")

set(CMAKE_CROSSCOMPILING "TRUE")

set(CMAKE_SYSTEM_LOADED 1)
