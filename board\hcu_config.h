/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file hcu_config.h
 * @brief 
 * 
 */




#ifndef __HCU_CONFIG_H__
#define __HCU_CONFIG_H__

#include "hcu_driver.h"

/* hcu_config0 */
extern hcu_state_t hcu_config0_State;
extern const hcu_user_config_t hcu_config0;

#endif


