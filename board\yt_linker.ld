/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file yt_linker.ld
 * @brief 
 * 
 */












/* MEMORY MAP */
MEMORY
{
    IVT   (RX) : ORIGIN = 0x0, LENGTH = 0x340
    BOOT   (RX) : ORIGIN = 0x340, LENGTH = 0x7fcc0
    IVT_RAM   (RW) : ORIGIN = 0x1fff8000, LENGTH = 0x400
    FBL_VAR   (RW) : ORIGIN = 0x1fff8400, LENGTH = 0x10
    RAM   (RW) : ORIGIN = 0x1fff8410, LENGTH = 0xf7f0
    STACK   (RW) : ORIGIN = 0x20007c00, LENGTH = 0x400
}
/* SECTIONS */
SECTIONS
{
    .IVT  : {
        
        IVT_start = .;
        isr_vector_region_start = .;
        KEEP(*(.isr_vector))
        isr_vector_region_end = .;
        
        IVT_end = .;
    } > IVT
   
    


    .TEXT  : {
        
        TEXT_start = .;
        rodata_region_start = .;
        *(.rodata)
        *(.rodata*)
        rodata_region_end = .;
        
        text_region_start = .;
        *(.text)
        *(.text*)
        text_region_end = .;
        
        TEXT_end = .;
    } > BOOT
   
    


    .ARM  : {
        
        ARM_start = .;
        ARM.exidx_region_start = .;
        *(.ARM.exidx)
        *(.ARM.exidx*)
        ARM.exidx_region_end = .;
        
        ARM_end = .;
    } > BOOT
   
    


    CODE_RAM_rom_start_not_align = .;
    CODE_RAM_rom_start = CODE_RAM_rom_start_not_align + (CODE_RAM_rom_start_not_align % 4);
   
    CODE_RAM_rom_end = CODE_RAM_rom_start + CODE_RAM_ram_end - CODE_RAM_ram_start;
    


    DATA_RAM_rom_start_not_align = CODE_RAM_rom_end;
    DATA_RAM_rom_start = DATA_RAM_rom_start_not_align + (DATA_RAM_rom_start_not_align % 4);
   
    DATA_RAM_rom_end = DATA_RAM_rom_start + DATA_RAM_ram_end - DATA_RAM_ram_start;
    


    .IVT_RAM  : {
        
        . = ALIGN(1024);
        IVT_RAM_start = .;
        . += 0X400;
        IVT_RAM_end = .;
    } > IVT_RAM
   
    


    .FBL_VAR  (NOLOAD): {
        
        . = ALIGN(8);
        FBL_VAR_start = .;
        fbl_bss_region_start = .;
        *(.fbl_bss)
        fbl_bss_region_end = .;
        
        . = ALIGN(8);
        FBL_VAR_end = .;
    } > FBL_VAR
   
    


    .BSS  (NOLOAD): {
        
        BSS_start = .;
        bss_region_start = .;
        *(.bss)
        *(.bss*)
        bss_region_end = .;
        
        BSS_end = .;
    } > RAM
   
    


    .CODE_RAM  : AT(CODE_RAM_rom_start) {
        
        . = ALIGN(4);
        CODE_RAM_ram_start = .;
        CODE_RAM_start = .;
        code_ram_region_start = .;
        *(.code_ram)
        code_ram_region_end = .;
        
        CODE_RAM_end = .;
        CODE_RAM_ram_end = .;
    } > RAM
   
    
    ASSERT((CODE_RAM_ram_end - CODE_RAM_ram_start) == (CODE_RAM_rom_end - CODE_RAM_rom_start), "Copy Section CODE_RAM Size non-aligned")


    .DATA_RAM  : AT(DATA_RAM_rom_start) {
        
        DATA_RAM_ram_start = .;
        DATA_RAM_start = .;
        data_region_start = .;
        *(.data)
        *(.data*)
        data_region_end = .;
        
        DATA_RAM_end = .;
        DATA_RAM_ram_end = .;
    } > RAM
   
    
    ASSERT((DATA_RAM_ram_end - DATA_RAM_ram_start) == (DATA_RAM_rom_end - DATA_RAM_rom_start), "Copy Section DATA_RAM Size non-aligned")


    .STACK  : {
        
        STACK_start = .;
        . += 1024;
        STACK_end = .;
    } > STACK
   
    


    IVT_memory_start = ORIGIN(IVT);
    IVT_memory_end = ORIGIN(IVT) + LENGTH(IVT);
    IVT_memory_size = LENGTH(IVT);
    BOOT_memory_start = ORIGIN(BOOT);
    BOOT_memory_end = ORIGIN(BOOT) + LENGTH(BOOT);
    BOOT_memory_size = LENGTH(BOOT);
    IVT_RAM_memory_start = ORIGIN(IVT_RAM);
    IVT_RAM_memory_end = ORIGIN(IVT_RAM) + LENGTH(IVT_RAM);
    IVT_RAM_memory_size = LENGTH(IVT_RAM);
    FBL_VAR_memory_start = ORIGIN(FBL_VAR);
    FBL_VAR_memory_end = ORIGIN(FBL_VAR) + LENGTH(FBL_VAR);
    FBL_VAR_memory_size = LENGTH(FBL_VAR);
    RAM_memory_start = ORIGIN(RAM);
    RAM_memory_end = ORIGIN(RAM) + LENGTH(RAM);
    RAM_memory_size = LENGTH(RAM);
    STACK_memory_start = ORIGIN(STACK);
    STACK_memory_end = ORIGIN(STACK) + LENGTH(STACK);
    STACK_memory_size = LENGTH(STACK);
}