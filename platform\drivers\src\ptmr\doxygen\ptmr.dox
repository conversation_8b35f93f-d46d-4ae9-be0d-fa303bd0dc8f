/*!
@defgroup ptmr Low Power Interrupt Timer (pTMR)
@brief The Low Power Periodic Interrupt Timer (pTMR) is a multi-channel timer module
generating independent pre-trigger and trigger outputs. These timer channels can operate
individually or can be chained together. The pTMR can operate in low power modes if
configured to do so. The pre-trigger and trigger outputs can be used to trigger other
modules on the device.
@details
The S32 SDK provides Peripheral Drivers for the Low Power Interrupt Timer (pTMR) module of S32 devices.

*/
