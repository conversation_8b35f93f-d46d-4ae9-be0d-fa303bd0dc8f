/*!
@defgroup pins Pins Driver (PINS)
@brief The YTMicro SDK provides Peripheral Drivers for the PINS module.
@details The module provides dedicated pad control to general-purpose pads that can be
configured as either inputs or outputs. The PINS module provides registers that enable
user software to read values from GPIO pads configured as inputs, and write values to
GPIO pads configured as outputs:
- When configured as output, you can write to an internal register to control the state
driven on the associated output pad.
- When configured as input, you can detect the state of the associated pad by reading
the value from an internal register.
- When configured as input and output, the pad value can be read back, which can be
used as a method of checking if the written value appeared on the pad.

Pins driver is based on PCTRL (Port Control) and GPIO (General-Purpose Input/Output) modules.

The PINS supports these following features:
* Pin interrupt
- Interrupt flag and enable registers for each pin
- Support for edge sensitive (rising, falling, both) or level sensitive (low, high) configured per pin
- Support for interrupt or DMA request configured per pin
- Asynchronous wake-up in low-power modes
* Digital input filter
- Digital input filter for each pin
- Individual enable or bypass control field per pin
- Selectable clock source for digital input filter with a five bit resolution on filter size
- Functional in GPIO modes
* Port control
- Individual pull control fields with pull-up, pull-down, and pull-disable support
- Individual drive strength field supporting high and low drive strength (only on supported pins)
- Individual slew rate field supporting fast and slow slew rates
- Individual input passive filter field supporting enable and disable of the individual input passive filter (only on supported pins)
- Individual mux control field supporting analog or pin disabled, GPIO, and up to 6 chip-specific digital functions
- Pad configuration fields are functional in all digital pin muxing modes
- Drive data to as many as 32 independent I/O channels
- Sample data from as many as 32 independent I/O channels
* External interrupt/DMA request support with:
- 1 to 4 system interrupt vectors for 1 to 4 interrupt sources with independent interrupt
masks. For 32 external interrupt sources (REQ pins), four groups have eight interrupt
sources each, and each of the four groups is assigned one system interrupt vector.
- 1 to 32 programmable digital glitch filters, one for each REQ pin
- 1 to 4 system DMA request channels up to 32 REQ pins, depending on device using
- Inversion control
- Control of analog path switches
- Safe mode behavior configuration

*/

## Integration guideline ##

### Compilation units ###

The following files need to be compiled in the project:
\verbatim
${SDK_PATH}\platform\drivers\src\pins\pins_driver.c
${SDK_PATH}\platform\drivers\src\pins\pins_port_hw_access.c
\endverbatim

### Include path ###

The following paths need to be added to the include path of the toolchain:
\verbatim
${SDK_PATH}\platform\drivers\inc
\endverbatim

### Compile symbols ###

No special symbols are required for this component

### Dependencies ###

*/
