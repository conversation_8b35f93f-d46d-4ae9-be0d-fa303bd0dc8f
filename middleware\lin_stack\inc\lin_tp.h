
 #ifndef LIN_TP_H
 #define LIN_TP_H





#include "lin_types.h"

/**
 * @brief see lin spec.
 * @param[in] inst ifc handle
 * @param[out] data data buffer
 */
void ld_get_raw (l_ifc_handle inst, l_u8* const  data);
/**
 * @brief see lin spec.
 * @param[in] inst ifc handle
 * @param[in] data data buffer
 */
void ld_put_raw (l_ifc_handle inst, const l_u8* const  data);
/**
 * @brief see lin spec
 * @param[in] inst ifc handle
 * @return l_u8 
 */
l_u8 ld_raw_tx_status (l_ifc_handle inst);
/**
 * @brief see lin spec
 * @param[in] inst ifc handle
 * @return l_u8 
 */
l_u8 ld_raw_rx_status (l_ifc_handle inst);
/**
 * @brief see lin spec.
 * @param[in] inst ifc handle
 * @param[in] length message length
 * @param[in] nad nad address, used for master node
 * @param[in] data data buffer
 */
void ld_send_message (l_ifc_handle inst,l_u16 length,l_u8 nad,const l_u8* const data);
/**
 * @brief see lin spec.
 * @param[in] inst ifc handle
 * @param[out] length message length
 * @param[out] NAD nad address, used for master node
 * @param[out] data data buffer
 */
void ld_receive_message (l_ifc_handle inst,l_u16* const length,l_u8* const NAD,l_u8* const data);

/**
 * @brief see lin spec.
 * @param[in] inst 
 * @return l_u8 
 */
l_u8 ld_tx_status (l_ifc_handle inst);

/**
 * @brief see lin spec.
 * @param[in] inst ifc handle
 * @return l_u8 
 */
l_u8 ld_rx_status (l_ifc_handle inst);
/**
 * @brief see lin spec.
 * @param[in] inst ifc handle
 */
void ld_init(l_ifc_handle inst);
/**
 * @brief see lin spec.
 * @param[in] inst ifc handle
 * @param[in] NAD  nad address
 * @param[in] start_index start index
 * @param[in] PIDs pid buffer,length is 4
 */
void ld_assign_frame_id_range (l_ifc_handle inst, l_u8 NAD, l_u8 start_index, const l_u8* const PIDs);
/**
 * @brief see lin spec.
 * @param[in] inst ifc handle
 * @param[in] initial_NAD init nad
 * @param[in] supplier_id supplier id
 * @param[in] function_id function id 
 * @param[in] new_NAD new nad
 */
void ld_assign_NAD(l_ifc_handle inst,l_u8 initial_NAD,l_u16 supplier_id,l_u16 function_id,l_u8 new_NAD);
/**
 * @brief see lin spec.
 * @param[in] inst ifc handle
 * @param[in] NAD nad address
 */
void ld_save_configuration (l_ifc_handle inst,l_u8 NAD);
/**
 * @brief see lin spec
 * @param[in] inst ifc handle
 * @param[out] data data buffer
 * @param[out] length length of data area (1 + n, NAD + PIDs)
 * @return l_u8 
 */
l_u8 ld_read_configuration(l_ifc_handle inst,l_u8 * const data,l_u8 * const length);
/**
 * @brief see lin spec
 * @param[in] inst ifc handle
 * @param[in] data data buffer
 * @param[in] length length of data area (1 + n, NAD + PIDs)
 * @return l_u8 set config result
 */
l_u8 ld_set_configuration(l_ifc_handle inst,const l_u8 * const data,l_u16 length);
/**
 * @brief see lin spec
 * @param[in] inst ifc handle
 * @param[in] NAD nad address
 * @param[in] id id
 * @param[in] byte byte
 * @param[in] mask mask
 * @param[in] invert invert
 * @param[in] new_NAD new nad address
 */
void ld_conditional_change_NAD (l_ifc_handle inst,l_u8 NAD,l_u8 id,l_u8 byte,l_u8 mask,l_u8 invert,l_u8 new_NAD);
/**
 * @brief see lin spec
 * @note the function need be implemented by user, just declare it here
 * @param[in] inst ifc handle
 * @param[in] id nad address
 * @param[in] data data buffer
 * @return l_u8 
 */
l_u8 ld_read_by_id_callout (l_ifc_handle inst,l_u8 id,l_u8* data);


/**
 * @brief lin tp general handle api, called by core layer, user need not call it
 * @param[in] inst ifc handle
 * @param[in] event tp layer event
 */
void lin_tp_handle(l_ifc_handle inst,lin_tp_event_t event);
/**
 * @brief lin tp sch handle api, mater node only, called by core layer, user need not call it
 * @param[in] inst ifc handle
 * @note call it each schedule end
 */
void lin_tp_sch_handle(l_ifc_handle inst);

/**
 * @brief lin tp timeout handle api, call by lin_timeout_handle, user need not call it
 * @param[in] inst ifc handle
 * @param[in] usedMs used time in microseconds
 */
void lin_tp_timeout_handle(l_ifc_handle inst,int usedMs);

/**
 * @brief set diag mode, master only
 * @param[in] inst ifc handle
 * @param[in] diagOnly l_true: diag only, l_false: normal mode
 */
void lin_set_diag_mode(l_ifc_handle inst,l_bool diagOnly);
#endif