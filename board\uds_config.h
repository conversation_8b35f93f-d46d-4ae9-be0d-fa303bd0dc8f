/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file uds_config.h
 * @brief 
 * 
 */


#ifndef UDS_CONFIG_H
#define UDS_CONFIG_H

#define UDS_ENABLE_LIN
#include "lin_core.h"
#include "lin_tp.h"
#include "uds_types.h"



#define UDS_CHANNEL_NUM (1U)

#define UDS_IP_Session_DISABLE
#define UDS_IP_TesterPresent_DISABLE
#define UDS_IP_ReadMemoryByAddress_DISABLE
#define UDS_IP_WriteMemoryByAddress_DISABLE
#define UDS_IP_RequestDownload_DISABLE
#define UDS_IP_TransferData_DISABLE
#define UDS_IP_RequestTransferExit_DISABLE
#define UDS_IP_RequestUpload_DISABLE
#define UDS_IP_RoutineControlEraseFlashMemory_DISABLE
#define UDS_IP_RoutineControlCrcCheck_DISABLE
#define UDS_IP_RoutineControlFlashDriverDownloaded_DISABLE
#define UDS_IP_SecurityAccess_DISABLE
#define UDS_IP_ECUResetSoftReset_DISABLE
#define UDS_BUILD_IN_FLASH_DISABLE


#define UDS_DEBUG

#define UDS_MAX_PENDING_NUM 100


extern const uds_config_t uds_config[UDS_CHANNEL_NUM];

extern void Uds_ConfigInit();

#endif /*UDS_CONFIG_H*/
