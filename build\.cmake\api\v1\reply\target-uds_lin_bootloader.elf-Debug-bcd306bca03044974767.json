{"artifacts": [{"path": "uds_lin_bootloader.elf"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "add_link_options", "include", "project", "target_link_options", "compilerSpecificCompileOptions", "configcore", "target_link_libraries", "compilerSpecificPlatformConfigAppForM", "target_compile_options", "target_compile_definitions", "target_include_directories", "target_sources"], "files": ["CMakeLists.txt", "cmake/gcc.cmake", "build/CMakeFiles/3.26.4/CMakeSystem.cmake", "cmake/Toolchain/GCC.cmake", "cmake/configCore.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 25, "parent": 0}, {"command": 3, "file": 0, "line": 6, "parent": 0}, {"file": 2, "parent": 2}, {"command": 2, "file": 2, "line": 6, "parent": 3}, {"file": 1, "parent": 4}, {"command": 1, "file": 1, "line": 82, "parent": 5}, {"command": 6, "file": 0, "line": 41, "parent": 0}, {"command": 5, "file": 4, "line": 284, "parent": 7}, {"command": 4, "file": 3, "line": 23, "parent": 8}, {"command": 4, "file": 3, "line": 29, "parent": 8}, {"command": 7, "file": 0, "line": 58, "parent": 0}, {"command": 8, "file": 0, "line": 66, "parent": 0}, {"command": 7, "file": 3, "line": 74, "parent": 12}, {"command": 7, "file": 0, "line": 73, "parent": 0}, {"command": 7, "file": 0, "line": 74, "parent": 0}, {"command": 9, "file": 3, "line": 16, "parent": 8}, {"command": 9, "file": 3, "line": 20, "parent": 8}, {"command": 9, "file": 3, "line": 30, "parent": 8}, {"command": 9, "file": 3, "line": 33, "parent": 8}, {"command": 10, "file": 4, "line": 188, "parent": 7}, {"command": 10, "file": 4, "line": 180, "parent": 7}, {"command": 10, "file": 0, "line": 48, "parent": 0}, {"command": 11, "file": 0, "line": 27, "parent": 0}, {"command": 12, "file": 0, "line": 32, "parent": 0}, {"command": 12, "file": 0, "line": 32, "parent": 0}, {"command": 12, "file": 0, "line": 32, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-mcpu=cortex-m33 -g"}, {"backtrace": 16, "fragment": "-mlittle-endian"}, {"backtrace": 17, "fragment": "-mthumb"}, {"backtrace": 18, "fragment": "-specs=nosys.specs"}, {"backtrace": 19, "fragment": "-Wall"}, {"backtrace": 19, "fragment": "-Werror=all"}, {"backtrace": 19, "fragment": "-Wno-error=comment"}, {"backtrace": 19, "fragment": "-g"}, {"backtrace": 19, "fragment": "-O1"}, {"backtrace": 19, "fragment": "-ffunction-sections"}, {"backtrace": 19, "fragment": "-fdata-sections"}, {"backtrace": 19, "fragment": "-Wno-error=unused-variable"}, {"backtrace": 19, "fragment": "-Wno-error=unused-parameter"}, {"backtrace": 19, "fragment": "-Wno-error=unused-function"}, {"backtrace": 19, "fragment": "-Wno-error=unused-but-set-variable"}, {"backtrace": 19, "fragment": "-Wno-error=unused-result"}, {"backtrace": 19, "fragment": "-Wno-error=maybe-uninitialized"}, {"backtrace": 19, "fragment": "-Wno-error=sign-compare"}, {"backtrace": 19, "fragment": "-Wno-error=strict-aliasing"}, {"backtrace": 19, "fragment": "-Wno-error=unknown-pragmas"}, {"backtrace": 19, "fragment": "-Wno-error=format"}, {"backtrace": 11, "fragment": "-fdiagnostics-color=always"}], "defines": [{"backtrace": 20, "define": "ARMCM33_DSP_FP"}, {"backtrace": 21, "define": "CORTEXM"}, {"backtrace": 22, "define": "CPU_YTM32B1MD1"}, {"backtrace": 22, "define": "YTM32B1MD1"}], "includes": [{"backtrace": 23, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/app"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include"}, {"backtrace": 11, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif"}], "language": "C", "sourceIndexes": [0, 1, 2, 3]}], "dependencies": [{"backtrace": 11, "id": "GENERATED_CONFIG_TARGET::@6890427a1f51a3e7e1df"}], "id": "uds_lin_bootloader.elf::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-mcpu=cortex-m33 -g", "role": "flags"}, {"fragment": "-mcpu=cortex-m33", "role": "flags"}, {"backtrace": 6, "fragment": "-Wl,--start-group", "role": "flags"}, {"backtrace": 9, "fragment": "-mcpu=cortex-m33", "role": "flags"}, {"backtrace": 10, "fragment": "-specs=nosys.specs", "role": "flags"}, {"backtrace": 11, "fragment": "-Wl,--whole-archive", "role": "libraries"}, {"backtrace": 11, "fragment": "libGENERATED_CONFIG_TARGET.a", "role": "libraries"}, {"backtrace": 11, "fragment": "libGENERATED_SDK_TARGET.a", "role": "libraries"}, {"backtrace": 11, "fragment": "-Wl,--no-whole-archive", "role": "libraries"}, {"backtrace": 13, "fragment": "-Xlinker --gc-sections", "role": "libraries"}, {"backtrace": 13, "fragment": "-nostartfiles", "role": "libraries"}, {"backtrace": 14, "fragment": "--entry=Reset_Handler", "role": "libraries"}, {"backtrace": 14, "fragment": "-TD:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board/yt_linker.ld", "role": "libraries"}, {"backtrace": 15, "fragment": "-Wl,-Map=uds_lin_bootloader.map", "role": "libraries"}, {"backtrace": 15, "fragment": "-Wl,--end-group", "role": "libraries"}, {"backtrace": 11, "fragment": "libGENERATED_CONFIG_TARGET.a", "role": "libraries"}, {"backtrace": 11, "fragment": "libGENERATED_SDK_TARGET.a", "role": "libraries"}], "language": "C"}, "name": "uds_lin_bootloader.elf", "nameOnDisk": "uds_lin_bootloader.elf", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "app/main.c", "sourceGroupIndex": 0}, {"backtrace": 24, "compileGroupIndex": 0, "path": "app/GenerateKeyExImpl.c", "sourceGroupIndex": 0}, {"backtrace": 25, "compileGroupIndex": 0, "path": "app/Uds_Service.c", "sourceGroupIndex": 0}, {"backtrace": 26, "compileGroupIndex": 0, "path": "app/boot_jump.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}