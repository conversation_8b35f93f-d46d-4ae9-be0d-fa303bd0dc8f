/**
 * @file uds_ip.h
 * @brief 
 * @version 0.1
 * @date 2024-08-31
 * 
 * (c) Copyright 2024 Yuntu Microelectronics co.,ltd.
 * 
 * All Rights Reserved
 * 
 */



#ifndef UDS_IP_H
#define UDS_IP_H




#include "uds.h"
#include "uds_types.h"


#ifdef UDS_IP_Session_ENABLE
/**
 * @brief DiagnosticSessionControl (0x10) service
 * 
 * @param[in] channel Uds channel
 * @param[in] data Received data
 * @param[in] dataLength Received data length
 * @param[in] param function param
 */
void UDS_IP_Session(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
#endif
#ifdef UDS_IP_TesterPresent_ENABLE
/**
 * @brief TesterPresent (0x3E) service
 * 
 * @param[in] channel Uds channel
 * @param[in] data Received data
 * @param[in] dataLength Received data length
 * @param[in] param function param
 */
void UDS_IP_TesterPresent(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
#endif
#ifdef UDS_IP_ReadMemoryByAddress_ENABLE
/**
 * @brief ReadMemoryByAddress (0x23) service
 * 
 * @param[in] channel Uds channel
 * @param[in] data Received data
 * @param[in] dataLength Received data length
 * @param[in] param function param
 */
void UDS_IP_ReadMemoryByAddress(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
#endif
#ifdef UDS_IP_WriteMemoryByAddress_ENABLE
/**
 * @brief WriteMemoryByAddress (0x3D) service
 * 
 * @param[in] channel Uds channel
 * @param[in] data Received data
 * @param[in] dataLength Received data length
 * @param[in] param function param
 */
void UDS_IP_WriteMemoryByAddress(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
#endif
#ifdef UDS_IP_RequestDownload_ENABLE
/**
 * @brief RequestDownload (0x34) service
 * 
 * @param[in] channel Uds channel
 * @param[in] data Received data
 * @param[in] dataLength Received data length
 * @param[in] param function param
 */
void UDS_IP_RequestDownload(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
#endif
#ifdef UDS_IP_RequestUpload_ENABLE
/**
 * @brief RequestUpload (0x35) service
 * 
 * @param[in] channel Uds channel
 * @param[in] data Received data
 * @param[in] dataLength Received data length
 * @param[in] param function param
 */
void UDS_IP_RequestUpload(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
#endif
#ifdef UDS_IP_TransferData_ENABLE
/**
 * @brief TransferData (0x36) service
 * 
 * @param[in] channel Uds channel
 * @param[in] data Received data
 * @param[in] dataLength Received data length
 * @param[in] param function param
 */
void UDS_IP_TransferData(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
#endif
#ifdef UDS_IP_RequestTransferExit_ENABLE
/**
 * @brief RequestTransferExit (0x37) service
 * 
 * @param[in] channel Uds channel
 * @param[in] data Received data
 * @param[in] dataLength Received data length
 * @param[in] param function param
 */
void UDS_IP_RequestTransferExit(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
#endif
#ifdef UDS_IP_RoutineControlEraseFlashMemory_ENABLE
/**
 * @brief RoutineControl (0x31) service Erase Flash Memory
 * 
 * @param[in] channel Uds channel
 * @param[in] data Received data, routineControlOptionRecord1-4:address, routineControlOptionRecord5-8:size,
 * @param[in] dataLength Received data length
 * @param[in] param function param
 */
void UDS_IP_RoutineControlEraseFlashMemory(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
#endif
#ifdef UDS_IP_RoutineControlCrcCheck_ENABLE
/**
 * @brief RoutineControl (0x31) service Crc Check
 * 
 * @param[in] channel Uds channel
 * @param[in] data Received data, routineControlOptionRecord1-4:address, routineControlOptionRecord5-8:size, routineControlOptionRecord9-10:crcResult
 * @param[in] dataLength Received data length
 * @param[in] param function param
 */
void UDS_IP_RoutineControlCrcCheck(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
#endif
#ifndef UDS_BUILD_IN_FLASH_ENABLE
#ifdef UDS_IP_RoutineControlFlashDriverDownloaded_ENABLE
/**
 * @brief RoutineControl (0x31) service flash driver downloaded notification
 * 
 * @param[in] channel Uds channel
 * @param[in] data Received data, routineControlOptionRecord1-4:address, routineControlOptionRecord5-8:size
 * @param[in] dataLength Received data length
 * @param[in] param function param
 */
void UDS_IP_RoutineControlFlashDriverDownloaded(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
#endif
#endif
#ifdef UDS_IP_SecurityAccess_ENABLE
/**
 * @brief SecurityAccess (0x27) service
 * 
 * @param[in] channel Uds channel
 * @param[in] data  Received data
 * @param[in] dataLength Received data length
 * @param[in] param function param
 */
void UDS_IP_SecurityAccess(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
#endif
#ifdef UDS_IP_ECUResetSoftReset_ENABLE
/**
 * @brief SecurityAccess (0x11) service subfuction03
 * 
 * @param[in] channel Uds channel
 * @param[in] data  Received data
 * @param[in] dataLength Received data length
 * @param[in] param function param
 */
void UDS_IP_ECUResetSoftReset(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
#endif
typedef enum
{
    UDS_IP_MEM_RAM,                /**< Memory type ram */
    UDS_IP_MEM_FLASH,              /**< Memory type flash */
    UDS_IP_MEM_INVALID,            /**< Memory type invalid */
}uds_ip_mem_t;

typedef enum
{
    UDS_RC_ERASE_FLASH               =  0xFF00u,       /**< Routine Control EraseMemory */
    UDS_RC_CRC_CHECK                 =  0xF000u,       /**< Routine Control CheckSum */
    UDS_RC_FLASHDRIVER_DOWNLOADED    =  0xF001u,       /**< Routine Control flash driver downloaded notification */
} uds_rc_id_t;

typedef struct
{
    uds_u32_t address;               /**< Address */
    uds_u32_t size;                  /**< Size */
    uds_u32_t leftSize;              /**< Left size */
    uds_sid_t sid;                   /**< Service Id */
    uds_u8_t bsc;                    /**< Block sequence counter */
    uds_bool_t started;              /**< Started state of RequestDownload or RequestUpload */
    uds_u8_t reserved;               /**< reserved */
}uds_ud_t;

typedef struct
{
    uds_ip_mem_t (*getMemoryType)(uds_u32_t address,uds_u32_t size);                        /**< getMemoryType */
    uds_s32_t (*readFlash)(uds_u32_t address, uds_u8_t* data, uds_u32_t dataLength);        /**< readFlash */
    uds_s32_t (*writeFlash)(uds_u32_t address, uds_u8_t* data, uds_u32_t dataLength);       /**< writeFlash */
    uds_s32_t (*eraseFlash)(uds_u32_t address, uds_u32_t dataLength);                       /**< eraseFlash */
    uds_s32_t (*readRam)(uds_u32_t address, uds_u8_t* data, uds_u32_t dataLength);          /**< readRam */
    uds_s32_t (*writeRam)(uds_u32_t address, uds_u8_t* data, uds_u32_t dataLength);         /**< writeRam */
} uds_ip_api_t;

extern uds_ip_api_t uds_global_ip_api;



#endif