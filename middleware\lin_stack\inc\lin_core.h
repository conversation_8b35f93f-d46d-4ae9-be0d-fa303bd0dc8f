/**
 * @file lin_core.h
 * @brief 
 * @version 0.1
 * @date 2023-10-25
 * 
 * (c) Copyright 2020-2022 Yuntu Microelectronics co.,ltd.
 * 
 * All Rights Reserved
 * 
 */


#ifndef LIN_CORE_H
#define LIN_CORE_H

#include "lin_types.h"
#include "lin_lib_config.h"


/**
 * @brief see lin spec.
 * @param[in] inst ifc handle
 */
void l_lfx_tx(l_ifc_handle inst);
/**
 * @brief see lin spec.
 * @param[in] inst ifc handle
 */
void l_lfx_rx(l_ifc_handle inst);
/**
 * @brief see lin spec.
 * @return l_bool 
 */
l_bool l_sys_init(void);
/**
 * @brief see lin spec
 * @param[in] inst ifc handle
 * @return l_u8 
 */
l_u8 l_sch_tick(l_ifc_handle inst);
/**
 * @brief see lin spec
 * @param[in] inst ifc handle
 * @param[in] schedule schedule handle
 * @param[in] entry entry index
 */
void l_sch_set(l_ifc_handle inst,l_schedule_handle schedule,l_u8 entry);
/**
 * @brief see lin spec
 * @param[in] handle signal handle
 * @param[in] v value
 */
void l_bool_wr(const l_signal_handle handle,l_bool v);
/**
 * @brief see lin spec
 * @param[in] handle signal handle
 * @return l_bool 
 */
l_bool l_bool_rd(const l_signal_handle handle);
/**
 * @brief see lin spec
 * @param[in] handle signal handle
 * @param[in] v value
 */
void l_u8_wr (l_signal_handle handle, l_u8 v);
/**
 * @brief see lin spec
 * @param[in] handle signal handle
 * @return l_u8 
 */
l_u8 l_u8_rd(const l_signal_handle handle);
/**
 * @brief see lin spec
 * @param[in] handle signal handle
 * @param[in] v value
 */
void l_u16_wr (l_signal_handle handle, l_u16 v);
/**
 * @brief see lin spec
 * @param[in] handle signal handle
 * @return l_u16 
 */
l_u16 l_u16_rd(l_signal_handle handle);
/**
 * @brief see lin spec
 * @param[in] handle signal handle
 * @param[in] start start index
 * @param[in] count count
 * @param[out] data data buffer
 */
void l_bytes_rd (l_signal_handle handle, l_u8 start,l_u8 count,l_u8* const data);
/**
 * @brief see lin spec
 * @param[in] handle signal handle
 * @param[in] start start index
 * @param[in] count count
 * @param[in] data data buffer
 */
void l_bytes_wr (l_signal_handle handle,l_u8 start,l_u8 count,const l_u8* const data);
/**
 * @brief see lin spec
 * @param[in] handle flag handle
 */
void l_flg_clr (l_flag_handle handle);
/**
 * @brief see lin spec
 * @param[in] handle flag handle
 * @return l_bool 
 */
l_bool l_flg_tst (l_flag_handle handle);
/**
 * @brief see lin spec, disable global interrupt
 * @return l_irqmask 
 */
l_irqmask l_sys_irq_disable (void);
/**
 * @brief see lin spec, restore global interrupt
 * @param[in] previous 
 */
void l_sys_irq_restore (l_irqmask previous);

/**
 * @brief see lin spec
 * @param[in] inst ifc handle
 */
void l_ifc_goto_sleep (l_ifc_handle inst);
/**
 * @brief see lin spec
 * @param[in] inst ifc handle
 */
void l_ifc_wake_up (l_ifc_handle inst);

/**
 * @brief see lin spec
 * @param[in] inst ifc handle
 */
void l_ifc_aux (l_ifc_handle inst);

/**
 * @brief see lin spec
 * @param[in] inst ifc handle
 * @return l_u16 status
 */
l_u16 l_ifc_read_status (l_ifc_handle inst);
/**
 * @brief see lin spec
 * @param[in] inst ifc handle
 * @return l_bool 
 */
l_bool l_ifc_init (l_ifc_handle inst);
/**
 * @brief rx handler for linflexd, install it with LINFlexD_DRV_InstallCallback
 * @param[in] hwInst hw instance
 * @param[in] param param
 */
void lin_frame_rx_handler(uint32_t hwInst, void *param);

/**
 * @brief back to last active schedule
 * @param[in] inst 
 */
void lin_sch_back(l_ifc_handle inst);
/**
 * @brief lin timeout handle, must call 500 microsecond
 * @param[in] inst ifc handle
 */
void lin_timeout_handle(l_ifc_handle inst);
#endif