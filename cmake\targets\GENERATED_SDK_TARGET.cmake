cmake_minimum_required(VERSION 3.16)



set(sources
    ${PROJ_DIR}/platform/drivers/src/clock/YTM32B1Mx/clock_YTM32B1Mx.c
    ${PROJ_DIR}/platform/drivers/src/pins/pins_driver.c
    ${PROJ_DIR}/platform/drivers/src/pins/pins_port_hw_access.c
    ${PROJ_DIR}/platform/drivers/src/interrupt/interrupt_manager.c
    ${PROJ_DIR}/platform/drivers/src/dma/dma_driver.c
    ${PROJ_DIR}/platform/drivers/src/dma/dma_hw_access.c
    ${PROJ_DIR}/platform/drivers/src/dma/dma_irq.c
    ${PROJ_DIR}/platform/drivers/src/linflexd/linflexd_lin_driver.c
    ${PROJ_DIR}/platform/drivers/src/linflexd/linflexd_lin_irq.c
    ${PROJ_DIR}/platform/drivers/src/ptmr/ptmr_driver.c
    ${PROJ_DIR}/platform/drivers/src/lptmr/lptmr_driver.c
    ${PROJ_DIR}/platform/drivers/src/lptmr/lptmr_hw_access.c
    ${PROJ_DIR}/platform/drivers/src/crc/crc_driver.c
    ${PROJ_DIR}/platform/drivers/src/crc/crc_hw_access.c
    ${PROJ_DIR}/platform/drivers/src/hcu/hcu_driver.c
    ${PROJ_DIR}/platform/drivers/src/hcu/hcu_irq.c
    ${PROJ_DIR}/platform/drivers/src/trng/trng_driver.c
    ${PROJ_DIR}/platform/drivers/src/trng/trng_hw_access.c
    ${PROJ_DIR}/platform/drivers/src/flash/flash_driver.c
    ${PROJ_DIR}/platform/devices/YTM32B1MD1/startup/system_YTM32B1MD1.c
    ${PROJ_DIR}/middleware/lin_stack/src/lin_core.c
    ${PROJ_DIR}/middleware/lin_stack/src/lin_tp.c
    ${PROJ_DIR}/middleware/uds/src/uds_ip.c
    ${PROJ_DIR}/middleware/uds/src/uds.c
    ${PROJ_DIR}/rtos/osif/osif_baremetal.c
)
set(includes
    ${PROJ_DIR}/platform/drivers/src/clock/YTM32B1Mx
    ${PROJ_DIR}/platform/drivers/src/pins
    ${PROJ_DIR}/platform/drivers/src/dma
    ${PROJ_DIR}/platform/drivers/src/linflexd
    ${PROJ_DIR}/platform/drivers/src/ptmr
    ${PROJ_DIR}/platform/drivers/src/lptmr
    ${PROJ_DIR}/platform/drivers/src/crc
    ${PROJ_DIR}/platform/drivers/src/hcu
    ${PROJ_DIR}/platform/drivers/src/trng
    ${PROJ_DIR}/platform/drivers/src/flash
    ${PROJ_DIR}/platform/drivers/inc
    ${PROJ_DIR}/platform/devices/common
    ${PROJ_DIR}/platform/devices
    ${PROJ_DIR}/platform/devices/YTM32B1MD1/include
    ${PROJ_DIR}/platform/devices/YTM32B1MD1/startup
    ${PROJ_DIR}/middleware/lin_stack/inc
    ${PROJ_DIR}/middleware/uds/inc
    ${PROJ_DIR}/CMSIS/Core/Include
    ${PROJ_DIR}/rtos/osif
)
set(priIncludes
)

add_library(GENERATED_SDK_TARGET STATIC ${sources})

target_include_directories(GENERATED_SDK_TARGET PUBLIC ${includes})


target_include_directories(GENERATED_SDK_TARGET PRIVATE ${priIncludes})
configcore(GENERATED_SDK_TARGET ${CMAKE_SOURCE_DIR})

target_compile_definitions(GENERATED_SDK_TARGET PUBLIC
    YTM32B1MD1
    CPU_YTM32B1MD1
)
target_compile_options(GENERATED_SDK_TARGET PUBLIC
    -fdiagnostics-color=always
)



target_link_libraries(GENERATED_SDK_TARGET
    GENERATED_CONFIG_TARGET
)
