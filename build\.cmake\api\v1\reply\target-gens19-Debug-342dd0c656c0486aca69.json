{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 92, "parent": 0}]}, "dependencies": [{"id": "uds_lin_bootloader.elf::@6890427a1f51a3e7e1df"}], "id": "gens19::@6890427a1f51a3e7e1df", "name": "gens19", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "build/CMakeFiles/gens19", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/gens19.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}