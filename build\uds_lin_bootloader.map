Archive member included to satisfy reference by file (symbol)

libGENERATED_CONFIG_TARGET.a(clock_config.c.o)
                              (--whole-archive)
libGENERATED_CONFIG_TARGET.a(pin_mux.c.o)
                              (--whole-archive)
libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
                              (--whole-archive)
libGENERATED_CONFIG_TARGET.a(startup.S.o)
                              (--whole-archive)
libGENERATED_CONFIG_TARGET.a(vector.S.o)
                              (--whole-archive)
libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                              (--whole-archive)
libGENERATED_CONFIG_TARGET.a(RamInit0.S.o)
                              (--whole-archive)
libGENERATED_CONFIG_TARGET.a(RamInit1.c.o)
                              (--whole-archive)
libGENERATED_CONFIG_TARGET.a(RamInit2.c.o)
                              (--whole-archive)
libGENERATED_CONFIG_TARGET.a(interrupt_config.c.o)
                              (--whole-archive)
libGENERATED_CONFIG_TARGET.a(dma_config.c.o)
                              (--whole-archive)
libGENERATED_CONFIG_TARGET.a(linflexd_lin_config.c.o)
                              (--whole-archive)
libGENERATED_CONFIG_TARGET.a(ptmr_config.c.o)
                              (--whole-archive)
libGENERATED_CONFIG_TARGET.a(lptmr_config.c.o)
                              (--whole-archive)
libGENERATED_CONFIG_TARGET.a(crc_config.c.o)
                              (--whole-archive)
libGENERATED_CONFIG_TARGET.a(hcu_config.c.o)
                              (--whole-archive)
libGENERATED_CONFIG_TARGET.a(flash_config.c.o)
                              (--whole-archive)
libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(pins_driver.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(pins_port_hw_access.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(dma_driver.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(dma_irq.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(linflexd_lin_irq.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(lptmr_hw_access.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(crc_driver.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(crc_hw_access.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(hcu_irq.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(trng_driver.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(flash_driver.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(system_YTM32B1MD1.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(lin_core.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(lin_tp.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(uds_ip.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(uds.c.o)
                              (--whole-archive)
libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
                              (--whole-archive)
c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_mulsf3.o)
                              libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o) (__aeabi_fmul)
c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubsf3.o)
                              libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o) (__aeabi_fsub)
c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivsf3.o)
                              libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o) (__aeabi_fdiv)
c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_fixunssfsi.o)
                              libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o) (__aeabi_f2uiz)
c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o)
                              libGENERATED_SDK_TARGET.a(ptmr_driver.c.o) (__aeabi_uldivmod)
c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
                              c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o) (__udivmoddi4)
c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_dvmd_tls.o)
                              c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o) (__aeabi_ldiv0)
c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-memcpy.o)
                              CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o (memcpy)
c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-rand.o)
                              CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o (srand)
c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-impure.o)
                              c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-rand.o) (_impure_ptr)

Discarded input sections

 .text          0x00000000        0x0 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
 .data          0x00000000        0x0 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
 .bss           0x00000000        0x0 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
 .text.pTMR0_IRQHandler
                0x00000000       0x12 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
 .rodata.key    0x00000000       0x10 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
 .text          0x00000000        0x0 CMakeFiles/uds_lin_bootloader.elf.dir/app/GenerateKeyExImpl.c.o
 .data          0x00000000        0x0 CMakeFiles/uds_lin_bootloader.elf.dir/app/GenerateKeyExImpl.c.o
 .bss           0x00000000        0x0 CMakeFiles/uds_lin_bootloader.elf.dir/app/GenerateKeyExImpl.c.o
 .text          0x00000000        0x0 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .data          0x00000000        0x0 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .bss           0x00000000        0x0 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .text          0x00000000        0x0 CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
 .data          0x00000000        0x0 CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
 .bss           0x00000000        0x0 CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
 .text.System_Reset
                0x00000000        0x8 CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
 .text.Boot_TimeService
                0x00000000       0x18 CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
 .bss.eclipseTime
                0x00000000        0x4 CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
 .text          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(clock_config.c.o)
 .data          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(clock_config.c.o)
 .bss           0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(clock_config.c.o)
 .text          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(pin_mux.c.o)
 .data          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(pin_mux.c.o)
 .bss           0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(pin_mux.c.o)
 .text          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .data          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .bss           0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .data          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(startup.S.o)
 .bss           0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(startup.S.o)
 .text          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(vector.S.o)
 .data          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(vector.S.o)
 .bss           0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(vector.S.o)
 .text          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .data          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .bss           0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.SysTick_Handler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.DMA0_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.DMA1_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.DMA2_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.DMA3_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.DMA4_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.DMA5_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.DMA6_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.DMA7_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.DMA8_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.DMA9_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.DMA10_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.DMA11_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.DMA12_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.DMA13_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.DMA14_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.DMA15_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.DMA_Error_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.EFM_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.EFM_Error_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.lpTMR0_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .text.HCU_IRQHandler
                0x00000000        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .data          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(RamInit0.S.o)
 .bss           0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(RamInit0.S.o)
 .text          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(RamInit1.c.o)
 .data          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(RamInit1.c.o)
 .bss           0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(RamInit1.c.o)
 .rodata.ZeroLayout
                0x00000000        0xc libGENERATED_CONFIG_TARGET.a(RamInit1.c.o)
 .text          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(RamInit2.c.o)
 .data          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(RamInit2.c.o)
 .bss           0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(RamInit2.c.o)
 .text          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(interrupt_config.c.o)
 .data          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(interrupt_config.c.o)
 .bss           0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(interrupt_config.c.o)
 .text          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(dma_config.c.o)
 .data          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(dma_config.c.o)
 .bss           0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(dma_config.c.o)
 .bss.dmaState  0x00000000       0x40 libGENERATED_CONFIG_TARGET.a(dma_config.c.o)
 .bss.dma_config0_State
                0x00000000       0x10 libGENERATED_CONFIG_TARGET.a(dma_config.c.o)
 .rodata.dmaChnConfigArray
                0x00000000        0x4 libGENERATED_CONFIG_TARGET.a(dma_config.c.o)
 .rodata.dmaChnState
                0x00000000        0x4 libGENERATED_CONFIG_TARGET.a(dma_config.c.o)
 .rodata.dmaController_InitConfig
                0x00000000        0x1 libGENERATED_CONFIG_TARGET.a(dma_config.c.o)
 .rodata.dma_config0
                0x00000000        0xc libGENERATED_CONFIG_TARGET.a(dma_config.c.o)
 .debug_info    0x00000000      0x423 libGENERATED_CONFIG_TARGET.a(dma_config.c.o)
 .debug_abbrev  0x00000000      0x108 libGENERATED_CONFIG_TARGET.a(dma_config.c.o)
 .debug_aranges
                0x00000000       0x18 libGENERATED_CONFIG_TARGET.a(dma_config.c.o)
 .debug_line    0x00000000      0x262 libGENERATED_CONFIG_TARGET.a(dma_config.c.o)
 .debug_str     0x00000000      0x6ee libGENERATED_CONFIG_TARGET.a(dma_config.c.o)
 .comment       0x00000000       0x4a libGENERATED_CONFIG_TARGET.a(dma_config.c.o)
 .ARM.attributes
                0x00000000       0x34 libGENERATED_CONFIG_TARGET.a(dma_config.c.o)
 .text          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(linflexd_lin_config.c.o)
 .data          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(linflexd_lin_config.c.o)
 .bss           0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(linflexd_lin_config.c.o)
 .text          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(ptmr_config.c.o)
 .data          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(ptmr_config.c.o)
 .bss           0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(ptmr_config.c.o)
 .rodata.ptmr_channel_0
                0x00000000        0xc libGENERATED_CONFIG_TARGET.a(ptmr_config.c.o)
 .text          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(lptmr_config.c.o)
 .data          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(lptmr_config.c.o)
 .bss           0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(lptmr_config.c.o)
 .text          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(crc_config.c.o)
 .data          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(crc_config.c.o)
 .bss           0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(crc_config.c.o)
 .rodata.crc_config0
                0x00000000        0x8 libGENERATED_CONFIG_TARGET.a(crc_config.c.o)
 .debug_info    0x00000000      0x154 libGENERATED_CONFIG_TARGET.a(crc_config.c.o)
 .debug_abbrev  0x00000000       0x9a libGENERATED_CONFIG_TARGET.a(crc_config.c.o)
 .debug_aranges
                0x00000000       0x18 libGENERATED_CONFIG_TARGET.a(crc_config.c.o)
 .debug_line    0x00000000      0x1e5 libGENERATED_CONFIG_TARGET.a(crc_config.c.o)
 .debug_str     0x00000000      0x2aa libGENERATED_CONFIG_TARGET.a(crc_config.c.o)
 .comment       0x00000000       0x4a libGENERATED_CONFIG_TARGET.a(crc_config.c.o)
 .ARM.attributes
                0x00000000       0x34 libGENERATED_CONFIG_TARGET.a(crc_config.c.o)
 .text          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(hcu_config.c.o)
 .data          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(hcu_config.c.o)
 .bss           0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(hcu_config.c.o)
 .bss.hcu_config0_State
                0x00000000       0x48 libGENERATED_CONFIG_TARGET.a(hcu_config.c.o)
 .rodata.hcu_config0
                0x00000000        0x4 libGENERATED_CONFIG_TARGET.a(hcu_config.c.o)
 .debug_info    0x00000000      0x6d8 libGENERATED_CONFIG_TARGET.a(hcu_config.c.o)
 .debug_abbrev  0x00000000       0xdb libGENERATED_CONFIG_TARGET.a(hcu_config.c.o)
 .debug_aranges
                0x00000000       0x18 libGENERATED_CONFIG_TARGET.a(hcu_config.c.o)
 .debug_line    0x00000000      0x2a5 libGENERATED_CONFIG_TARGET.a(hcu_config.c.o)
 .debug_str     0x00000000      0xeb4 libGENERATED_CONFIG_TARGET.a(hcu_config.c.o)
 .comment       0x00000000       0x4a libGENERATED_CONFIG_TARGET.a(hcu_config.c.o)
 .ARM.attributes
                0x00000000       0x34 libGENERATED_CONFIG_TARGET.a(hcu_config.c.o)
 .text          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(flash_config.c.o)
 .data          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(flash_config.c.o)
 .bss           0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(flash_config.c.o)
 .text          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 .data          0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 .bss           0x00000000        0x0 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .text.CLOCK_DRV_ResetModule
                0x00000000       0x28 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .text.CLOCK_DRV_SetModuleClock
                0x00000000       0x2c libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .text.CLOCK_SYS_GetCurrentConfiguration
                0x00000000        0xc libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .text.CLOCK_SYS_GetErrorCallback
                0x00000000       0x1c libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .text.CLOCK_SYS_SetConfiguration
                0x00000000        0x8 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_SetPullSel
                0x00000000       0x3a libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_SetMuxModeSel
                0x00000000        0x8 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_SetPinIntSel
                0x00000000       0x18 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_GetPinIntSel
                0x00000000        0xc libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_ClearPinIntFlagCmd
                0x00000000        0x8 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_EnableDigitalFilter
                0x00000000       0x12 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_DisableDigitalFilter
                0x00000000       0x12 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_ConfigDigitalFilter
                0x00000000       0x24 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_GetPortIntFlag
                0x00000000        0x4 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_ClearPortIntFlagCmd
                0x00000000        0x8 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_GetPinsDirection
                0x00000000        0x4 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_SetPinDirection
                0x00000000       0x16 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_SetPinsDirection
                0x00000000        0x4 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_SetPortInputDisable
                0x00000000        0x6 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_GetPortInputDisable
                0x00000000        0x6 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_WritePin
                0x00000000       0x16 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_WritePins
                0x00000000        0x4 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_GetPinsOutput
                0x00000000        0x4 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_SetPins
                0x00000000        0x4 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_ClearPins
                0x00000000        0x4 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text.PINS_DRV_ReadPins
                0x00000000        0x4 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(pins_port_hw_access.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(pins_port_hw_access.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(pins_port_hw_access.c.o)
 .text.PINS_SetMuxModeSel
                0x00000000       0x16 libGENERATED_SDK_TARGET.a(pins_port_hw_access.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
 .text.INT_SYS_GetPriority
                0x00000000       0x24 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
 .text.INT_SYS_ClearPending
                0x00000000       0x20 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
 .text.INT_SYS_SetPending
                0x00000000       0x20 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
 .text.INT_SYS_GetPending
                0x00000000       0x24 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_ClearStructure
                0x00000000       0x12 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_InstallCallback
                0x00000000       0x1c libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_ChannelInit
                0x00000000       0x74 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_Init
                0x00000000       0x80 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_ReleaseChannel
                0x00000000       0x34 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_Deinit
                0x00000000       0x50 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_ConfigSingleBlockTransfer
                0x00000000      0x150 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_ConfigMultiBlockTransfer
                0x00000000       0x60 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_StartChannel
                0x00000000       0x18 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_StopChannel
                0x00000000       0x18 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_SetChannelRequestAndTrigger
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_ClearCTS
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_SetSrcAddr
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_SetSrcOffset
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_SetSrcReadChunkSize
                0x00000000       0x26 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_SetSrcLastAddrAdjustment
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_SetDestLastAddrAdjustment
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_SetDestAddr
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_SetDestOffset
                0x00000000       0x16 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_SetDestWriteChunkSize
                0x00000000       0x24 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_SetTransferLoopBlockSize
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_SetTriggerLoopIterationCount
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_GetRemainingTriggerIterationsCount
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_SetRamReloadLink
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_DisableRequestsOnTransferComplete
                0x00000000       0x34 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_ConfigureInterrupt
                0x00000000       0x84 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_CancelTransfer
                0x00000000       0x18 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_TriggerSwRequest
                0x00000000       0x1e libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_PushConfigToSCTS
                0x00000000       0x8e libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_PushConfigToReg
                0x00000000      0x1e8 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_ConfigLoopTransfer
                0x00000000       0x28 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_ConfigRamReloadTransfer
                0x00000000      0x15c libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text.DMA_DRV_GetChannelStatus
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .rodata.s_dmaIrqId
                0x00000000       0x20 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .text.DMA_CancelTransfer
                0x00000000       0x12 libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .text.DMA_CancelTransferWithError
                0x00000000       0x12 libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .text.DMA_SetErrorIntCmd
                0x00000000       0x30 libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .text.DMA_CTSClearReg
                0x00000000       0x38 libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .text.DMA_Init
                0x00000000       0x24 libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .text.DMA_CTSSetAttribute
                0x00000000       0x2e libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .text.DMA_CTSSetNbytes
                0x00000000       0x56 libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .text.DMA_CTSSetTransferLoopOffset
                0x00000000       0x48 libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .text.DMA_CTSSetRamReloadLink
                0x00000000        0xa libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .text.DMA_CTSSetChannelLoopLink
                0x00000000       0x6a libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .text.DMA_CTSSetTriggerCount
                0x00000000       0x5a libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .text.DMA_CTSGetCurrentTriggerCount
                0x00000000       0x20 libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .text.DMAMUX_Init
                0x00000000       0x12 libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(dma_irq.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(dma_irq.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(dma_irq.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .text.LINFlexD_DRV_GetDefaultConfig
                0x00000000        0xe libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .text.LINFlexD_DRV_DataDiscard
                0x00000000       0x18 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .text.LINFlexD_DRV_MasterTransferBlocking
                0x00000000       0x54 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .text.LINFlexD_DRV_GoToSleepMode
                0x00000000       0x30 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .text.LINFlexD_DRV_GotoIdleState
                0x00000000       0x18 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .text.LINFlexD_DRV_SendWakeupSignal
                0x00000000       0x18 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .text.LINFlexD_DRV_GetCurrentNodeState
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .text.LINFlexD_DRV_EnableIRQ
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .text.LINFlexD_DRV_DisableIRQ
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .text.LINFlexD_GetFilterMatchId
                0x00000000       0x2c libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .text.LINFlexD_DRV_GetChecksumType
                0x00000000       0x40 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(linflexd_lin_irq.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(linflexd_lin_irq.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(linflexd_lin_irq.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .text.pTMR_DRV_GetDefaultConfig
                0x00000000        0x6 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .text.pTMR_DRV_GetDefaultChanConfig
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .text.pTMR_DRV_StopTimerChannels
                0x00000000       0x18 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .text.pTMR_DRV_SetTimerPeriodByUs
                0x00000000       0x58 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .text.pTMR_DRV_InitChannel
                0x00000000       0xac libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .text.pTMR_DRV_SetTimerPeriodByCount
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .text.pTMR_DRV_GetTimerPeriodByUs
                0x00000000       0x74 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .text.pTMR_DRV_GetTimerPeriodByCount
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .text.pTMR_DRV_GetCurrentTimerUs
                0x00000000       0x5c libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .text.pTMR_DRV_GetCurrentTimerCount
                0x00000000        0xc libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .text.pTMR_DRV_EnableTimerChannelInterrupt
                0x00000000       0x18 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .text.pTMR_DRV_DisableTimerChannelInterrupt
                0x00000000       0x18 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .text.pTMR_DRV_GetInterruptFlagTimerChannels
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .text.pTMR_DRV_ClearInterruptFlagTimerChannels
                0x00000000       0x24 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .text.lpTMR_DRV_InitConfigStruct
                0x00000000       0x1c libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .text.lpTMR_DRV_GetConfig
                0x00000000       0x54 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .text.lpTMR_DRV_SetCompareValueByCount
                0x00000000       0x38 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .text.lpTMR_DRV_GetCompareValueByCount
                0x00000000        0xc libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .text.lpTMR_DRV_SetCompareValueByUs
                0x00000000       0x88 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .text.lpTMR_DRV_GetCompareValueByUs
                0x00000000       0x94 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .text.lpTMR_DRV_GetCompareFlag
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .text.lpTMR_DRV_IsRunning
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .text.lpTMR_DRV_SetInterrupt
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .text.lpTMR_DRV_GetCounterValueByCount
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .text.lpTMR_DRV_StopCounter
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .text.lpTMR_DRV_SetPinConfiguration
                0x00000000       0x28 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(lptmr_hw_access.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(lptmr_hw_access.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(lptmr_hw_access.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .text.CRC_DRV_Deinit
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .text.CRC_DRV_GetCrc32
                0x00000000       0x18 libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .text.CRC_DRV_GetCrc16
                0x00000000       0x18 libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .text.CRC_DRV_GetCrc8
                0x00000000       0x18 libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .text.CRC_DRV_WriteData
                0x00000000       0x1c libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .text.CRC_DRV_WriteData16
                0x00000000       0x1c libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .text.CRC_DRV_WriteData32
                0x00000000       0x1c libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .text.CRC_DRV_GetCrcResult
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .text.CRC_DRV_Configure
                0x00000000       0x58 libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .text.CRC_DRV_Init
                0x00000000       0x1c libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .text.CRC_DRV_GetConfig
                0x00000000       0x30 libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .text.CRC_DRV_GetDefaultConfig
                0x00000000       0x16 libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .debug_info    0x00000000     0x1079 libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .debug_abbrev  0x00000000      0x324 libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .debug_loc     0x00000000      0x8fc libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .debug_aranges
                0x00000000       0x78 libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .debug_ranges  0x00000000       0xb0 libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .debug_line    0x00000000      0x7a7 libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .debug_str     0x00000000      0xf22 libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .comment       0x00000000       0x4a libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .debug_frame   0x00000000      0x104 libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .ARM.attributes
                0x00000000       0x34 libGENERATED_SDK_TARGET.a(crc_driver.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(crc_hw_access.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(crc_hw_access.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(crc_hw_access.c.o)
 .text.CRC_Init
                0x00000000       0x2a libGENERATED_SDK_TARGET.a(crc_hw_access.c.o)
 .text.CRC_GetCrcResult
                0x00000000       0x1c libGENERATED_SDK_TARGET.a(crc_hw_access.c.o)
 .debug_info    0x00000000      0x56f libGENERATED_SDK_TARGET.a(crc_hw_access.c.o)
 .debug_abbrev  0x00000000      0x20b libGENERATED_SDK_TARGET.a(crc_hw_access.c.o)
 .debug_loc     0x00000000      0x268 libGENERATED_SDK_TARGET.a(crc_hw_access.c.o)
 .debug_aranges
                0x00000000       0x28 libGENERATED_SDK_TARGET.a(crc_hw_access.c.o)
 .debug_ranges  0x00000000       0x18 libGENERATED_SDK_TARGET.a(crc_hw_access.c.o)
 .debug_line    0x00000000      0x3db libGENERATED_SDK_TARGET.a(crc_hw_access.c.o)
 .debug_str     0x00000000      0x38d libGENERATED_SDK_TARGET.a(crc_hw_access.c.o)
 .comment       0x00000000       0x4a libGENERATED_SDK_TARGET.a(crc_hw_access.c.o)
 .debug_frame   0x00000000       0x30 libGENERATED_SDK_TARGET.a(crc_hw_access.c.o)
 .ARM.attributes
                0x00000000       0x34 libGENERATED_SDK_TARGET.a(crc_hw_access.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .text.HCU_ConfigAlgorithm
                0x00000000       0xdc libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .text.HCU_DRV_CompleteDMA
                0x00000000       0x80 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .text.HCU_RunOneLoop
                0x00000000      0x100 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .text.HCU_DRV_ConfigDMA
                0x00000000       0xc8 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .text.HCU_DRV_DeInit
                0x00000000       0x50 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .text.HCU_DRV_CfgSwapping
                0x00000000       0x18 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .text.HCU_DRV_Init
                0x00000000       0x60 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .text.HCU_DRV_ClearODFlag
                0x00000000        0xc libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .text.HCU_DRV_InstallCallback
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .text.HCU_DRV_LoadUserKey
                0x00000000       0x58 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .text.HCU_DRV_EncryptECB
                0x00000000       0x78 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .text.HCU_DRV_DecryptECB
                0x00000000       0x7c libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .text.HCU_DRV_EncryptCBC
                0x00000000       0x90 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .text.HCU_DRV_DecryptCBC
                0x00000000       0x90 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .text.HCU_DRV_GenerateMAC
                0x00000000      0x10c libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .text.HCU_DRV_AuthorizeMAC
                0x00000000      0x114 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(hcu_irq.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(hcu_irq.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(hcu_irq.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(trng_driver.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(trng_driver.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(trng_driver.c.o)
 .text.TRNG_DRV_Get_Ent
                0x00000000       0x60 libGENERATED_SDK_TARGET.a(trng_driver.c.o)
 .text.TRNG_DRV_GetStatus
                0x00000000       0x34 libGENERATED_SDK_TARGET.a(trng_driver.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
 .text.TRNG_Disable
                0x00000000        0x8 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
 .text.TRNG_SWReset
                0x00000000        0xe libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
 .text.TRNG_Get_FRQCNT
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
 .text.TRNG_Get_SCMC
                0x00000000        0x6 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
 .text.TRNG_Get_ENT
                0x00000000        0x8 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
 .text.TRNG_GetStatusFlag
                0x00000000        0xc libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
 .text.TRNG_ClearStatusFlag
                0x00000000       0x26 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_GetDefaultConfig
                0x00000000        0xe libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_GetBusyStatus
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_SetDisableGlobalInt
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_SetReadVerify
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_EraseBlock
                0x00000000       0x78 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_EraseSector
                0x00000000       0xc0 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_EraseSectorQuick
                0x00000000      0x130 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_Program
                0x00000000       0x84 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_CheckSum
                0x00000000       0x58 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_DisableCmdCompleteInterrupt
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_Deinit
                0x00000000       0x48 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_SetAsyncMode
                0x00000000       0x5c libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_DisableReadCollisionInterrupt
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_EnableSingleBitFaultInterrupt
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_DisableSingleBitFaultInterrupt
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_EnableDoubleBitFaultInterrupt
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_DisableDoubleBitFaultInterrupt
                0x00000000       0x10 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_EraseNVR
                0x00000000       0x70 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_ProgramNVR
                0x00000000       0xac libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_ReadNVR
                0x00000000       0x94 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_BootSwap
                0x00000000       0x5c libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_BootSwap
                0x00000000       0x4c libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_DRV_LoadAESKey
                0x00000000       0x6c libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(system_YTM32B1MD1.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(system_YTM32B1MD1.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(system_YTM32B1MD1.c.o)
 .text.SystemCoreClockUpdate
                0x00000000       0x70 libGENERATED_SDK_TARGET.a(system_YTM32B1MD1.c.o)
 .text.SystemSoftwareReset
                0x00000000       0x24 libGENERATED_SDK_TARGET.a(system_YTM32B1MD1.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.lin_set_update_flag
                0x00000000       0x24 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.l_sch_set
                0x00000000        0x2 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.l_sch_tick
                0x00000000        0x4 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.l_bool_wr
                0x00000000       0x40 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.l_bool_rd
                0x00000000       0x30 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.l_u8_wr  0x00000000       0x40 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.l_u8_rd  0x00000000       0x28 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.l_u16_wr
                0x00000000       0x54 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.l_u16_rd
                0x00000000       0x30 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.l_bytes_rd
                0x00000000       0x3c libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.l_flg_clr
                0x00000000       0x70 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.l_flg_tst
                0x00000000       0x68 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.l_bytes_wr
                0x00000000       0x5c libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.l_ifc_read_status
                0x00000000       0x28 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.l_ifc_goto_sleep
                0x00000000       0x28 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.l_ifc_wake_up
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.l_ifc_aux
                0x00000000        0x2 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.lin_sch_back
                0x00000000       0x18 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.lin_timeout_handle
                0x00000000       0xec libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text.lin_is_func
                0x00000000       0x28 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text.lin_tp_sch_handle
                0x00000000       0xdc libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text.lin_tp_timeout_handle
                0x00000000       0xb8 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text.ld_raw_tx_status
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text.ld_raw_rx_status
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text.ld_is_ready
                0x00000000       0x14 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text.ld_check_response
                0x00000000       0x30 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text.ld_assign_frame_id_range
                0x00000000       0x70 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text.ld_assign_NAD
                0x00000000       0x70 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text.ld_save_configuration
                0x00000000       0x58 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text.ld_read_configuration
                0x00000000       0x48 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text.ld_set_configuration
                0x00000000       0x74 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text.ld_conditional_change_NAD
                0x00000000       0x70 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text.lin_set_diag_mode
                0x00000000        0x2 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(uds_ip.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(uds_ip.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(uds_ip.c.o)
 .debug_line    0x00000000        0x0 libGENERATED_SDK_TARGET.a(uds_ip.c.o)
 .debug_str     0x00000000      0x144 libGENERATED_SDK_TARGET.a(uds_ip.c.o)
 .comment       0x00000000       0x4a libGENERATED_SDK_TARGET.a(uds_ip.c.o)
 .ARM.attributes
                0x00000000       0x34 libGENERATED_SDK_TARGET.a(uds_ip.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(uds.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(uds.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(uds.c.o)
 .text.Uds_GetSecureLevel
                0x00000000       0x28 libGENERATED_SDK_TARGET.a(uds.c.o)
 .text          0x00000000        0x0 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
 .data          0x00000000        0x0 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
 .bss           0x00000000        0x0 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
 .text.OSIF_TimeDelay
                0x00000000       0x60 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
 .text.OSIF_GetMilliseconds
                0x00000000        0xc libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
 .text.OSIF_MutexLock
                0x00000000        0x4 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
 .text.OSIF_MutexUnlock
                0x00000000        0x4 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
 .text.OSIF_MutexCreate
                0x00000000        0x4 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
 .text.OSIF_MutexDestroy
                0x00000000        0x4 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
 .text.OSIF_SemaWait
                0x00000000       0x84 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
 .data.first_init.0
                0x00000000        0x1 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
 .text          0x00000000      0x168 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_mulsf3.o)
 .data          0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_mulsf3.o)
 .bss           0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_mulsf3.o)
 .debug_frame   0x00000000       0x24 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_mulsf3.o)
 .ARM.attributes
                0x00000000       0x22 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_mulsf3.o)
 .data          0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubsf3.o)
 .bss           0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubsf3.o)
 .data          0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivsf3.o)
 .bss           0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivsf3.o)
 .data          0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_fixunssfsi.o)
 .bss           0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_fixunssfsi.o)
 .data          0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o)
 .bss           0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o)
 .data          0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
 .bss           0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
 .ARM.extab     0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
 .data          0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_dvmd_tls.o)
 .bss           0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_dvmd_tls.o)
 .data          0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-memcpy.o)
 .bss           0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-memcpy.o)
 .text          0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-rand.o)
 .data          0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-rand.o)
 .bss           0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-rand.o)
 .text          0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-impure.o)
 .data          0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-impure.o)
 .bss           0x00000000        0x0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-impure.o)
 .rodata._global_impure_ptr
                0x00000000        0x4 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-impure.o)

Memory Configuration

Name             Origin             Length             Attributes
IVT              0x00000000         0x00000340         xr
BOOT             0x00000340         0x0007fcc0         xr
IVT_RAM          0x1fff8000         0x00000400         rw
FBL_VAR          0x1fff8400         0x00000010         rw
RAM              0x1fff8410         0x0000f7f0         rw
STACK            0x20007c00         0x00000400         rw
*default*        0x00000000         0xffffffff

Linker script and memory map

START GROUP
LOAD CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
LOAD CMakeFiles/uds_lin_bootloader.elf.dir/app/GenerateKeyExImpl.c.o
LOAD CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
LOAD CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
LOAD libGENERATED_CONFIG_TARGET.a
LOAD libGENERATED_SDK_TARGET.a
END GROUP
LOAD libGENERATED_CONFIG_TARGET.a
LOAD libGENERATED_SDK_TARGET.a
LOAD c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libstdc++.a
LOAD c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libm.a
START GROUP
LOAD c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a
LOAD c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a
END GROUP
START GROUP
LOAD c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a
LOAD c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a
LOAD c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libnosys.a
END GROUP

.IVT            0x00000000      0x340
                0x00000000                IVT_start = .
                0x00000000                isr_vector_region_start = .
 *(.isr_vector)
 .isr_vector    0x00000000      0x340 libGENERATED_CONFIG_TARGET.a(vector.S.o)
                0x00000340                isr_vector_region_end = .
                0x00000340                IVT_end = .

.TEXT           0x00000340     0x5e98
                0x00000340                TEXT_start = .
                0x00000340                rodata_region_start = .
 *(.rodata)
 .rodata        0x00000340        0x8 libGENERATED_SDK_TARGET.a(system_YTM32B1MD1.c.o)
 *(.rodata*)
 .rodata.clock_config0CmuConfig
                0x00000348       0x20 libGENERATED_CONFIG_TARGET.a(clock_config.c.o)
                0x00000348                clock_config0CmuConfig
 .rodata.clock_config0ScuConfig
                0x00000368       0x1c libGENERATED_CONFIG_TARGET.a(clock_config.c.o)
                0x00000368                clock_config0ScuConfig
 .rodata.LinConfig_0_Motor1State_Cycl_offsets
                0x00000384        0x4 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .rodata.LinConfig_0_Motor1State_Cycl_signals
                0x00000388        0x4 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .rodata.LinConfig_0_Motor1State_Event_offsets
                0x0000038c        0x2 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 *fill*         0x0000038e        0x2 
 .rodata.LinConfig_0_Motor1State_Event_signals
                0x00000390        0x2 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 *fill*         0x00000392        0x2 
 .rodata.LinConfig_0_Motor1_Dynamic_offsets
                0x00000394        0x1 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 *fill*         0x00000395        0x3 
 .rodata.LinConfig_0_Motor1_Dynamic_signals
                0x00000398        0x1 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 *fill*         0x00000399        0x3 
 .rodata.LinConfig_0_MotorControl_offsets
                0x0000039c        0x3 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 *fill*         0x0000039f        0x1 
 .rodata.LinConfig_0_MotorControl_signals
                0x000003a0        0x3 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 *fill*         0x000003a3        0x1 
 .rodata.LinConfig_0_config
                0x000003a4       0x28 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
                0x000003a4                LinConfig_0_config
 .rodata.LinConfig_0_frames
                0x000003cc       0x8c libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .rodata.LinConfig_0_signals
                0x00000458       0x48 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .rodata.LinConfig_0_slave_config
                0x000004a0       0x14 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .rodata.LinConfig_0_tp_config
                0x000004b4        0xc libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .rodata.CopyLayout
                0x000004c0       0x20 libGENERATED_CONFIG_TARGET.a(RamInit1.c.o)
                0x000004c0                CopyLayout
 .rodata.PTMR_Config
                0x000004e0        0x1 libGENERATED_CONFIG_TARGET.a(ptmr_config.c.o)
                0x000004e0                PTMR_Config
 *fill*         0x000004e1        0x3 
 .rodata.LPTMR_Config
                0x000004e4       0x10 libGENERATED_CONFIG_TARGET.a(lptmr_config.c.o)
                0x000004e4                LPTMR_Config
 .rodata.Inst0_rule_0_rxData
                0x000004f4        0x1 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 *fill*         0x000004f5        0x3 
 .rodata.Inst0_rule_0_rxMask
                0x000004f8        0x1 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 *fill*         0x000004f9        0x3 
 .rodata.Inst0_rule_1_rxData
                0x000004fc        0x1 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 *fill*         0x000004fd        0x3 
 .rodata.Inst0_rule_1_rxMask
                0x00000500        0x1 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 *fill*         0x00000501        0x3 
 .rodata.Inst0_rule_2_rxData
                0x00000504        0x4 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 .rodata.Inst0_rule_2_rxMask
                0x00000508        0x4 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 .rodata.Inst0_rule_3_rxData
                0x0000050c        0x1 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 *fill*         0x0000050d        0x3 
 .rodata.Inst0_rule_3_rxMask
                0x00000510        0x1 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 *fill*         0x00000511        0x3 
 .rodata.Inst0_rule_4_rxData
                0x00000514        0x1 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 *fill*         0x00000515        0x3 
 .rodata.Inst0_rule_4_rxMask
                0x00000518        0x1 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 *fill*         0x00000519        0x3 
 .rodata.Inst0_rule_5_rxData
                0x0000051c        0x1 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 *fill*         0x0000051d        0x3 
 .rodata.Inst0_rule_5_rxMask
                0x00000520        0x1 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 *fill*         0x00000521        0x3 
 .rodata.Inst0_rule_6_rxData
                0x00000524        0x4 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 .rodata.Inst0_rule_6_rxMask
                0x00000528        0x4 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 .rodata.Inst0_rule_7_rxData
                0x0000052c        0x1 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 *fill*         0x0000052d        0x3 
 .rodata.Inst0_rule_7_rxMask
                0x00000530        0x1 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 *fill*         0x00000531        0x3 
 .rodata.Inst0_rule_8_rxData
                0x00000534        0x1 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 *fill*         0x00000535        0x3 
 .rodata.Inst0_rule_8_rxMask
                0x00000538        0x1 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 *fill*         0x00000539        0x3 
 .rodata.uds_config
                0x0000053c       0x20 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
                0x0000053c                uds_config
 .rodata.uds_rule_config0
                0x0000055c       0xd8 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 .rodata.clockNameMappings
                0x00000634       0x70 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
                0x00000634                clockNameMappings
 .rodata.s_LINFlexDBase
                0x000006a4        0xc libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .rodata.s_LINFlexDClkName
                0x000006b0        0x3 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 *fill*         0x000006b3        0x1 
 .rodata.s_LINFlexDIntVec
                0x000006b4        0x6 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 *fill*         0x000006ba        0x2 
 .rodata.g_LinLINFlexDIsr
                0x000006bc        0xc libGENERATED_SDK_TARGET.a(linflexd_lin_irq.c.o)
                0x000006bc                g_LinLINFlexDIsr
 .rodata.ptmrIrqId
                0x000006c8        0x8 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .rodata.s_efmReadCollisionIrqId
                0x000006d0        0x4 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .rodata.lin_make_slave_response_pdu.str1.4
                0x000006d4        0x6 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
                0x000006da                rodata_region_end = .
                0x000006da                text_region_start = .
 *(.text)
 *fill*         0x000006da        0x2 
 .text          0x000006dc       0x5c libGENERATED_CONFIG_TARGET.a(startup.S.o)
                0x000006dc                Reset_Handler
 .text          0x00000738       0x94 libGENERATED_CONFIG_TARGET.a(RamInit0.S.o)
                0x00000738                RamInit0
 .text          0x000007cc      0x21c c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubsf3.o)
                0x000007cc                __aeabi_frsub
                0x000007d4                __subsf3
                0x000007d4                __aeabi_fsub
                0x000007d8                __aeabi_fadd
                0x000007d8                __addsf3
                0x00000938                __aeabi_ui2f
                0x00000938                __floatunsisf
                0x00000940                __aeabi_i2f
                0x00000940                __floatsisf
                0x0000095c                __aeabi_ul2f
                0x0000095c                __floatundisf
                0x0000096c                __aeabi_l2f
                0x0000096c                __floatdisf
 .text          0x000009e8      0x2a0 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivsf3.o)
                0x000009e8                __mulsf3
                0x000009e8                __aeabi_fmul
                0x00000b50                __aeabi_fdiv
                0x00000b50                __divsf3
 .text          0x00000c88       0x40 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_fixunssfsi.o)
                0x00000c88                __fixunssfsi
                0x00000c88                __aeabi_f2uiz
 .text          0x00000cc8       0x30 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o)
                0x00000cc8                __aeabi_uldivmod
 .text          0x00000cf8      0x2e8 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
                0x00000cf8                __udivmoddi4
 .text          0x00000fe0        0x4 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_dvmd_tls.o)
                0x00000fe0                __aeabi_idiv0
                0x00000fe0                __aeabi_ldiv0
 .text          0x00000fe4       0xec c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-memcpy.o)
                0x00000fe4                memcpy
 *(.text*)
 .text.ld_read_by_id_callout
                0x000010d0        0x4 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
                0x000010d0                ld_read_by_id_callout
 .text.main     0x000010d4       0xb4 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
                0x000010d4                main
 .text.lpTMR0_IRQHandler
                0x00001188       0x40 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
                0x00001188                lpTMR0_IRQHandler
 .text.GenerateKeyEx
                0x000011c8       0x2c CMakeFiles/uds_lin_bootloader.elf.dir/app/GenerateKeyExImpl.c.o
                0x000011c8                GenerateKeyEx
 .text.getMemoryTypeA
                0x000011f4       0x2c CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .text.writeRamA
                0x00001220        0xc CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .text.readRamA
                0x0000122c       0x12 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .text.readFlashA
                0x0000123e       0x12 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .text.eraseFlashA
                0x00001250       0x74 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .text.writeFlashA
                0x000012c4       0xac CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .text.UDS_IP_TesterPresentA
                0x00001370       0x36 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
                0x00001370                UDS_IP_TesterPresentA
 .text.UDS_IP_SessionA
                0x000013a6       0x3c CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
                0x000013a6                UDS_IP_SessionA
 *fill*         0x000013e2        0x2 
 .text.UDS_IP_SecurityAccessA
                0x000013e4      0x164 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
                0x000013e4                UDS_IP_SecurityAccessA
 .text.UDS_IP_RoutineControlEraseFlashMemoryA
                0x00001548       0xc0 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
                0x00001548                UDS_IP_RoutineControlEraseFlashMemoryA
 .text.UDS_IP_RequestDownloadA
                0x00001608      0x114 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
                0x00001608                UDS_IP_RequestDownloadA
 .text.UDS_IP_TransferDataA
                0x0000171c      0x1d0 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
                0x0000171c                UDS_IP_TransferDataA
 .text.UDS_IP_RequestTransferExitA
                0x000018ec       0x48 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
                0x000018ec                UDS_IP_RequestTransferExitA
 .text.UDS_IP_RoutineControlCrcCheckA
                0x00001934      0x110 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
                0x00001934                UDS_IP_RoutineControlCrcCheckA
 .text.UDS_IP_ECUResetSoftResetA
                0x00001a44       0x36 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
                0x00001a44                UDS_IP_ECUResetSoftResetA
 .text.shutdown_drivers
                0x00001a7a       0x32 CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
                0x00001a7a                shutdown_drivers
 .text.udsRxCallback
                0x00001aac       0x1c CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
                0x00001aac                udsRxCallback
 .text.bootup_application
                0x00001ac8       0x1c CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
                0x00001ac8                bootup_application
 .text.JumpTo_Application
                0x00001ae4       0x28 CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
                0x00001ae4                JumpTo_Application
 .text.StayInBoot_Init
                0x00001b0c       0x4c CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
                0x00001b0c                StayInBoot_Init
 .text.StayInBoot_Task
                0x00001b58       0xa0 CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
                0x00001b58                StayInBoot_Task
 .text.lin_config_init
                0x00001bf8       0x48 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
                0x00001bf8                lin_config_init
 .text.VectorTableCopy
                0x00001c40       0x38 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c40                VectorTableCopy
 .text.DefaultISR
                0x00001c78        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c78                DefaultISR
 .text.NMI_Handler
                0x00001c7a        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c7a                NMI_Handler
 .text.HardFault_Handler
                0x00001c7c        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c7c                HardFault_Handler
 .text.MemManage_Handler
                0x00001c7e        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c7e                MemManage_Handler
 .text.BusFault_Handler
                0x00001c80        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c80                BusFault_Handler
 .text.UsageFault_Handler
                0x00001c82        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c82                UsageFault_Handler
 .text.SVC_Handler
                0x00001c84        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c84                SVC_Handler
 .text.DebugMon_Handler
                0x00001c86        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c86                DebugMon_Handler
 .text.PendSV_Handler
                0x00001c88        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c88                PendSV_Handler
 .text.FPU_IRQHandler
                0x00001c8a        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c8a                FPU_IRQHandler
 .text.PCU_IRQHandler
                0x00001c8c        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c8c                PCU_IRQHandler
 .text.EFM_Ecc_IRQHandler
                0x00001c8e        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c8e                EFM_Ecc_IRQHandler
 .text.WDG0_IRQHandler
                0x00001c90        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c90                WDG0_IRQHandler
 .text.I2C0_Master_IRQHandler
                0x00001c92        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c92                I2C0_Master_IRQHandler
 .text.I2C0_Slave_IRQHandler
                0x00001c94        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c94                I2C0_Slave_IRQHandler
 .text.SPI0_IRQHandler
                0x00001c96        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c96                SPI0_IRQHandler
 .text.SPI1_IRQHandler
                0x00001c98        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c98                SPI1_IRQHandler
 .text.SPI2_IRQHandler
                0x00001c9a        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c9a                SPI2_IRQHandler
 .text.I2C1_Master_IRQHandler
                0x00001c9c        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c9c                I2C1_Master_IRQHandler
 .text.LINFlexD0_IRQHandler
                0x00001c9e        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001c9e                LINFlexD0_IRQHandler
 .text.LINFlexD1_IRQHandler
                0x00001ca0        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001ca0                LINFlexD1_IRQHandler
 .text.LINFlexD2_IRQHandler
                0x00001ca2        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001ca2                LINFlexD2_IRQHandler
 .text.ADC0_IRQHandler
                0x00001ca4        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001ca4                ADC0_IRQHandler
 .text.ACMP0_IRQHandler
                0x00001ca6        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001ca6                ACMP0_IRQHandler
 .text.EMU0_SB_IRQHandler
                0x00001ca8        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001ca8                EMU0_SB_IRQHandler
 .text.EMU0_DB_IRQHandler
                0x00001caa        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001caa                EMU0_DB_IRQHandler
 .text.RTC_IRQHandler
                0x00001cac        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cac                RTC_IRQHandler
 .text.RTC_Seconds_IRQHandler
                0x00001cae        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cae                RTC_Seconds_IRQHandler
 .text.pTMR0_Ch0_IRQHandler
                0x00001cb0        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cb0                pTMR0_Ch0_IRQHandler
 .text.pTMR0_Ch1_IRQHandler
                0x00001cb2        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cb2                pTMR0_Ch1_IRQHandler
 .text.pTMR0_Ch2_IRQHandler
                0x00001cb4        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cb4                pTMR0_Ch2_IRQHandler
 .text.pTMR0_Ch3_IRQHandler
                0x00001cb6        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cb6                pTMR0_Ch3_IRQHandler
 .text.PTU0_IRQHandler
                0x00001cb8        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cb8                PTU0_IRQHandler
 .text.SCU_IRQHandler
                0x00001cba        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cba                SCU_IRQHandler
 .text.GPIOA_IRQHandler
                0x00001cbc        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cbc                GPIOA_IRQHandler
 .text.GPIOB_IRQHandler
                0x00001cbe        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cbe                GPIOB_IRQHandler
 .text.GPIOC_IRQHandler
                0x00001cc0        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cc0                GPIOC_IRQHandler
 .text.GPIOD_IRQHandler
                0x00001cc2        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cc2                GPIOD_IRQHandler
 .text.GPIOE_IRQHandler
                0x00001cc4        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cc4                GPIOE_IRQHandler
 .text.CAN0_ORed_IRQHandler
                0x00001cc6        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cc6                CAN0_ORed_IRQHandler
 .text.CAN0_Error_IRQHandler
                0x00001cc8        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cc8                CAN0_Error_IRQHandler
 .text.CAN0_Wake_Up_IRQHandler
                0x00001cca        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cca                CAN0_Wake_Up_IRQHandler
 .text.CAN0_ORed_0_15_MB_IRQHandler
                0x00001ccc        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001ccc                CAN0_ORed_0_15_MB_IRQHandler
 .text.CAN0_ORed_16_31_MB_IRQHandler
                0x00001cce        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cce                CAN0_ORed_16_31_MB_IRQHandler
 .text.CAN0_ORed_32_47_MB_IRQHandler
                0x00001cd0        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cd0                CAN0_ORed_32_47_MB_IRQHandler
 .text.CAN0_ORed_48_63_MB_IRQHandler
                0x00001cd2        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cd2                CAN0_ORed_48_63_MB_IRQHandler
 .text.CAN1_ORed_IRQHandler
                0x00001cd4        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cd4                CAN1_ORed_IRQHandler
 .text.CAN1_Error_IRQHandler
                0x00001cd6        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cd6                CAN1_Error_IRQHandler
 .text.CAN1_Wake_Up_IRQHandler
                0x00001cd8        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cd8                CAN1_Wake_Up_IRQHandler
 .text.CAN1_ORed_0_15_MB_IRQHandler
                0x00001cda        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cda                CAN1_ORed_0_15_MB_IRQHandler
 .text.CAN1_ORed_16_31_MB_IRQHandler
                0x00001cdc        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cdc                CAN1_ORed_16_31_MB_IRQHandler
 .text.CAN2_ORed_IRQHandler
                0x00001cde        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cde                CAN2_ORed_IRQHandler
 .text.CAN2_Error_IRQHandler
                0x00001ce0        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001ce0                CAN2_Error_IRQHandler
 .text.CAN2_Wake_Up_IRQHandler
                0x00001ce2        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001ce2                CAN2_Wake_Up_IRQHandler
 .text.CAN2_ORed_0_15_MB_IRQHandler
                0x00001ce4        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001ce4                CAN2_ORed_0_15_MB_IRQHandler
 .text.CAN2_ORed_16_31_MB_IRQHandler
                0x00001ce6        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001ce6                CAN2_ORed_16_31_MB_IRQHandler
 .text.eTMR0_Ch0_Ch1_IRQHandler
                0x00001ce8        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001ce8                eTMR0_Ch0_Ch1_IRQHandler
 .text.eTMR0_Ch2_Ch3_IRQHandler
                0x00001cea        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cea                eTMR0_Ch2_Ch3_IRQHandler
 .text.eTMR0_Ch4_Ch5_IRQHandler
                0x00001cec        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cec                eTMR0_Ch4_Ch5_IRQHandler
 .text.eTMR0_Ch6_Ch7_IRQHandler
                0x00001cee        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cee                eTMR0_Ch6_Ch7_IRQHandler
 .text.eTMR0_Fault_IRQHandler
                0x00001cf0        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cf0                eTMR0_Fault_IRQHandler
 .text.eTMR0_Ovf_IRQHandler
                0x00001cf2        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cf2                eTMR0_Ovf_IRQHandler
 .text.eTMR1_Ch0_Ch1_IRQHandler
                0x00001cf4        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cf4                eTMR1_Ch0_Ch1_IRQHandler
 .text.eTMR1_Ch2_Ch3_IRQHandler
                0x00001cf6        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cf6                eTMR1_Ch2_Ch3_IRQHandler
 .text.eTMR1_Ch4_Ch5_IRQHandler
                0x00001cf8        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cf8                eTMR1_Ch4_Ch5_IRQHandler
 .text.eTMR1_Ch6_Ch7_IRQHandler
                0x00001cfa        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cfa                eTMR1_Ch6_Ch7_IRQHandler
 .text.eTMR1_Fault_IRQHandler
                0x00001cfc        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cfc                eTMR1_Fault_IRQHandler
 .text.eTMR1_Ovf_IRQHandler
                0x00001cfe        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001cfe                eTMR1_Ovf_IRQHandler
 .text.eTMR2_Ch0_Ch1_IRQHandler
                0x00001d00        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d00                eTMR2_Ch0_Ch1_IRQHandler
 .text.eTMR2_Ch2_Ch3_IRQHandler
                0x00001d02        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d02                eTMR2_Ch2_Ch3_IRQHandler
 .text.eTMR2_Ch4_Ch5_IRQHandler
                0x00001d04        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d04                eTMR2_Ch4_Ch5_IRQHandler
 .text.eTMR2_Ch6_Ch7_IRQHandler
                0x00001d06        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d06                eTMR2_Ch6_Ch7_IRQHandler
 .text.eTMR2_Fault_IRQHandler
                0x00001d08        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d08                eTMR2_Fault_IRQHandler
 .text.eTMR2_Ovf_IRQHandler
                0x00001d0a        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d0a                eTMR2_Ovf_IRQHandler
 .text.eTMR3_Ch0_Ch1_IRQHandler
                0x00001d0c        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d0c                eTMR3_Ch0_Ch1_IRQHandler
 .text.eTMR3_Ch2_Ch3_IRQHandler
                0x00001d0e        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d0e                eTMR3_Ch2_Ch3_IRQHandler
 .text.eTMR3_Ch4_Ch5_IRQHandler
                0x00001d10        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d10                eTMR3_Ch4_Ch5_IRQHandler
 .text.eTMR3_Ch6_Ch7_IRQHandler
                0x00001d12        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d12                eTMR3_Ch6_Ch7_IRQHandler
 .text.eTMR3_Fault_IRQHandler
                0x00001d14        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d14                eTMR3_Fault_IRQHandler
 .text.eTMR3_Ovf_IRQHandler
                0x00001d16        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d16                eTMR3_Ovf_IRQHandler
 .text.TRNG_IRQHandler
                0x00001d18        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d18                TRNG_IRQHandler
 .text.TMR0_Ch0_IRQHandler
                0x00001d1a        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d1a                TMR0_Ch0_IRQHandler
 .text.TMR0_Ch1_IRQHandler
                0x00001d1c        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d1c                TMR0_Ch1_IRQHandler
 .text.TMR0_Ch2_IRQHandler
                0x00001d1e        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d1e                TMR0_Ch2_IRQHandler
 .text.TMR0_Ch3_IRQHandler
                0x00001d20        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d20                TMR0_Ch3_IRQHandler
 .text.SPI3_IRQHandler
                0x00001d22        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d22                SPI3_IRQHandler
 .text.SENT0_IRQHandler
                0x00001d24        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d24                SENT0_IRQHandler
 .text.WKU_IRQHandler
                0x00001d26        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d26                WKU_IRQHandler
 .text.ALIGN_0_IRQHandler
                0x00001d28        0x2 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                0x00001d28                ALIGN_0_IRQHandler
 *fill*         0x00001d2a        0x2 
 .text.RamInit1
                0x00001d2c      0x104 libGENERATED_CONFIG_TARGET.a(RamInit1.c.o)
                0x00001d2c                RamInit1
 .text.RamInit2
                0x00001e30        0x2 libGENERATED_CONFIG_TARGET.a(RamInit2.c.o)
                0x00001e30                RamInit2
 .text.INT_SYS_ConfigInit
                0x00001e32       0x36 libGENERATED_CONFIG_TARGET.a(interrupt_config.c.o)
                0x00001e32                INT_SYS_ConfigInit
 .text.Uds_ConfigInit
                0x00001e68       0x20 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
                0x00001e68                Uds_ConfigInit
 .text.CLOCK_SYS_ConfigureSystemClock
                0x00001e88       0x30 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .text.CLOCK_SYS_WaitFXOSCValid
                0x00001eb8       0x68 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
                0x00001eb8                CLOCK_SYS_WaitFXOSCValid
 .text.CLOCK_DRV_GetPllFreq
                0x00001f20       0x50 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
                0x00001f20                CLOCK_DRV_GetPllFreq
 .text.CLOCK_DRV_GetFreq
                0x00001f70      0x148 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
                0x00001f70                CLOCK_DRV_GetFreq
 .text.CLOCK_SYS_Init
                0x000020b8       0x18 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
                0x000020b8                CLOCK_SYS_Init
 .text.CLOCK_SYS_GetFreq
                0x000020d0        0x8 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
                0x000020d0                CLOCK_SYS_GetFreq
 .text.CLOCK_DRV_Init
                0x000020d8      0x498 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
                0x000020d8                CLOCK_DRV_Init
 .text.CLOCK_SYS_UpdateConfiguration
                0x00002570      0x104 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
                0x00002570                CLOCK_SYS_UpdateConfiguration
 .text.PINS_DRV_Init
                0x00002674       0x20 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
                0x00002674                PINS_DRV_Init
 .text.PINS_DRV_TogglePins
                0x00002694        0x4 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
                0x00002694                PINS_DRV_TogglePins
 .text.PINS_Init
                0x00002698      0x12e libGENERATED_SDK_TARGET.a(pins_port_hw_access.c.o)
                0x00002698                PINS_Init
 *fill*         0x000027c6        0x2 
 .text.INT_SYS_InstallHandler
                0x000027c8       0x20 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
                0x000027c8                INT_SYS_InstallHandler
 .text.INT_SYS_EnableIRQ
                0x000027e8       0x1c libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
                0x000027e8                INT_SYS_EnableIRQ
 .text.INT_SYS_DisableIRQ
                0x00002804       0x28 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
                0x00002804                INT_SYS_DisableIRQ
 .text.INT_SYS_EnableIRQGlobal
                0x0000282c       0x1c libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
                0x0000282c                INT_SYS_EnableIRQGlobal
 .text.INT_SYS_DisableIRQGlobal
                0x00002848       0x10 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
                0x00002848                INT_SYS_DisableIRQGlobal
 .text.INT_SYS_SetPriority
                0x00002858       0x28 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
                0x00002858                INT_SYS_SetPriority
 .text.DMA_DRV_IRQHandler
                0x00002880       0x34 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
                0x00002880                DMA_DRV_IRQHandler
 .text.DMA_DRV_ErrorIRQHandler
                0x000028b4       0x44 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
                0x000028b4                DMA_DRV_ErrorIRQHandler
 .text.DMA_DRV_GetDmaRegBaseAddr
                0x000028f8        0x8 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
                0x000028f8                DMA_DRV_GetDmaRegBaseAddr
 .text.DMA_SetDmaRequestCmd
                0x00002900       0x4c libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
                0x00002900                DMA_SetDmaRequestCmd
 .text.DMA0_IRQHandler
                0x0000294c        0xa libGENERATED_SDK_TARGET.a(dma_irq.c.o)
                0x0000294c                DMA0_IRQHandler
 .text.DMA1_IRQHandler
                0x00002956        0xa libGENERATED_SDK_TARGET.a(dma_irq.c.o)
                0x00002956                DMA1_IRQHandler
 .text.DMA2_IRQHandler
                0x00002960        0xa libGENERATED_SDK_TARGET.a(dma_irq.c.o)
                0x00002960                DMA2_IRQHandler
 .text.DMA3_IRQHandler
                0x0000296a        0xa libGENERATED_SDK_TARGET.a(dma_irq.c.o)
                0x0000296a                DMA3_IRQHandler
 .text.DMA4_IRQHandler
                0x00002974        0xa libGENERATED_SDK_TARGET.a(dma_irq.c.o)
                0x00002974                DMA4_IRQHandler
 .text.DMA5_IRQHandler
                0x0000297e        0xa libGENERATED_SDK_TARGET.a(dma_irq.c.o)
                0x0000297e                DMA5_IRQHandler
 .text.DMA6_IRQHandler
                0x00002988        0xa libGENERATED_SDK_TARGET.a(dma_irq.c.o)
                0x00002988                DMA6_IRQHandler
 .text.DMA7_IRQHandler
                0x00002992        0xa libGENERATED_SDK_TARGET.a(dma_irq.c.o)
                0x00002992                DMA7_IRQHandler
 .text.DMA8_IRQHandler
                0x0000299c        0xa libGENERATED_SDK_TARGET.a(dma_irq.c.o)
                0x0000299c                DMA8_IRQHandler
 .text.DMA9_IRQHandler
                0x000029a6        0xa libGENERATED_SDK_TARGET.a(dma_irq.c.o)
                0x000029a6                DMA9_IRQHandler
 .text.DMA10_IRQHandler
                0x000029b0        0xa libGENERATED_SDK_TARGET.a(dma_irq.c.o)
                0x000029b0                DMA10_IRQHandler
 .text.DMA11_IRQHandler
                0x000029ba        0xa libGENERATED_SDK_TARGET.a(dma_irq.c.o)
                0x000029ba                DMA11_IRQHandler
 .text.DMA12_IRQHandler
                0x000029c4        0xa libGENERATED_SDK_TARGET.a(dma_irq.c.o)
                0x000029c4                DMA12_IRQHandler
 .text.DMA13_IRQHandler
                0x000029ce        0xa libGENERATED_SDK_TARGET.a(dma_irq.c.o)
                0x000029ce                DMA13_IRQHandler
 .text.DMA14_IRQHandler
                0x000029d8        0xa libGENERATED_SDK_TARGET.a(dma_irq.c.o)
                0x000029d8                DMA14_IRQHandler
 .text.DMA15_IRQHandler
                0x000029e2        0xa libGENERATED_SDK_TARGET.a(dma_irq.c.o)
                0x000029e2                DMA15_IRQHandler
 .text.DMA_Error_IRQHandler
                0x000029ec       0x28 libGENERATED_SDK_TARGET.a(dma_irq.c.o)
                0x000029ec                DMA_Error_IRQHandler
 .text.LINFlexD_DRV_Deinit
                0x00002a14       0x2c libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
                0x00002a14                LINFlexD_DRV_Deinit
 .text.LINFlexD_DRV_SetBaudRate
                0x00002a40       0xd8 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
                0x00002a40                LINFlexD_DRV_SetBaudRate
 .text.LINFlexD_DRV_Init
                0x00002b18      0x1b0 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
                0x00002b18                LINFlexD_DRV_Init
 .text.LINFlexD_DRV_InstallCallback
                0x00002cc8       0x10 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
                0x00002cc8                LINFlexD_DRV_InstallCallback
 .text.LINFlexD_DRV_MasterTransfer
                0x00002cd8       0xbc libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
                0x00002cd8                LINFlexD_DRV_MasterTransfer
 .text.LINFlexD_DRV_SlaveResponse
                0x00002d94       0xa4 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
                0x00002d94                LINFlexD_DRV_SlaveResponse
 .text.LINFlexD_DRV_AbortTransferData
                0x00002e38       0x34 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
                0x00002e38                LINFlexD_DRV_AbortTransferData
 .text.LINFlexD_LIN_DRV_TxIRQHandler
                0x00002e6c       0x44 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
                0x00002e6c                LINFlexD_LIN_DRV_TxIRQHandler
 .text.LINFlexD_LIN_DRV_ErrIRQHandler
                0x00002eb0      0x100 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
                0x00002eb0                LINFlexD_LIN_DRV_ErrIRQHandler
 .text.LINFlexD_LIN_DRV_FilterResponse
                0x00002fb0       0x84 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
                0x00002fb0                LINFlexD_LIN_DRV_FilterResponse
 .text.LINFlexD_LIN_DRV_RxIRQHandler
                0x00003034      0x100 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
                0x00003034                LINFlexD_LIN_DRV_RxIRQHandler
 .text.LINFlexD_LIN_DRV_IRQHandler
                0x00003134       0x38 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
                0x00003134                LINFlexD_LIN_DRV_IRQHandler
 .text.LINFlexD_DRV_ProcessParity
                0x0000316c       0x4a libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
                0x0000316c                LINFlexD_DRV_ProcessParity
 .text.LINFlexD0_LIN_IRQHandler
                0x000031b6        0xa libGENERATED_SDK_TARGET.a(linflexd_lin_irq.c.o)
                0x000031b6                LINFlexD0_LIN_IRQHandler
 .text.LINFlexD1_LIN_IRQHandler
                0x000031c0        0xa libGENERATED_SDK_TARGET.a(linflexd_lin_irq.c.o)
                0x000031c0                LINFlexD1_LIN_IRQHandler
 .text.LINFlexD2_LIN_IRQHandler
                0x000031ca        0xa libGENERATED_SDK_TARGET.a(linflexd_lin_irq.c.o)
                0x000031ca                LINFlexD2_LIN_IRQHandler
 .text.pTMR_DRV_Init
                0x000031d4       0x8c libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
                0x000031d4                pTMR_DRV_Init
 .text.pTMR_DRV_Deinit
                0x00003260       0x7c libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
                0x00003260                pTMR_DRV_Deinit
 .text.pTMR_DRV_StartTimerChannels
                0x000032dc       0x18 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
                0x000032dc                pTMR_DRV_StartTimerChannels
 .text.lptmr_ChooseClkConfig
                0x000032f4       0xdc libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .text.lpTMR_DRV_Deinit
                0x000033d0       0x18 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
                0x000033d0                lpTMR_DRV_Deinit
 .text.lpTMR_DRV_SetConfig
                0x000033e8      0x12c libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
                0x000033e8                lpTMR_DRV_SetConfig
 .text.lpTMR_DRV_Init
                0x00003514       0x1c libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
                0x00003514                lpTMR_DRV_Init
 .text.lpTMR_DRV_ClearCompareFlag
                0x00003530       0x10 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
                0x00003530                lpTMR_DRV_ClearCompareFlag
 .text.lpTMR_DRV_StartCounter
                0x00003540       0x10 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
                0x00003540                lpTMR_DRV_StartCounter
 .text.lpTMR_Init
                0x00003550       0x18 libGENERATED_SDK_TARGET.a(lptmr_hw_access.c.o)
                0x00003550                lpTMR_Init
 .text.HCU_DRV_DoneMAC
                0x00003568       0xac libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .text.HCU_DRV_IRQHandler
                0x00003614      0x1ac libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
                0x00003614                HCU_DRV_IRQHandler
 .text.HCU_IRQHandler
                0x000037c0        0x8 libGENERATED_SDK_TARGET.a(hcu_irq.c.o)
                0x000037c0                HCU_IRQHandler
 .text.TRNG_DRV_GetDefaultConfig
                0x000037c8       0x44 libGENERATED_SDK_TARGET.a(trng_driver.c.o)
                0x000037c8                TRNG_DRV_GetDefaultConfig
 .text.TRNG_DRV_Init
                0x0000380c       0x4c libGENERATED_SDK_TARGET.a(trng_driver.c.o)
                0x0000380c                TRNG_DRV_Init
 .text.TRNG_DRV_DeInit
                0x00003858       0x10 libGENERATED_SDK_TARGET.a(trng_driver.c.o)
                0x00003858                TRNG_DRV_DeInit
 .text.TRNG_Enable
                0x00003868        0xa libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
                0x00003868                TRNG_Enable
 *fill*         0x00003872        0x2 
 .text.TRNG_HW_Init
                0x00003874       0x30 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
                0x00003874                TRNG_HW_Init
 .text.TRNG_Set_CTRL
                0x000038a4       0x50 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
                0x000038a4                TRNG_Set_CTRL
 .text.TRNG_Set_SDCTL
                0x000038f4       0x24 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
                0x000038f4                TRNG_Set_SDCTL
 .text.TRNG_Set_FRQMIN
                0x00003918       0x20 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
                0x00003918                TRNG_Set_FRQMIN
 .text.TRNG_Set_FRQMAX
                0x00003938       0x20 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
                0x00003938                TRNG_Set_FRQMAX
 .text.TRNG_Set_SCML
                0x00003958       0x24 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
                0x00003958                TRNG_Set_SCML
 .text.FLASH_EventCallback
                0x0000397c       0x18 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .text.FLASH_GetSectorSize
                0x00003994       0x24 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
                0x00003994                FLASH_GetSectorSize
 .text.FLASH_DRV_DoneIRQHandler
                0x000039b8      0x18c libGENERATED_SDK_TARGET.a(flash_driver.c.o)
                0x000039b8                FLASH_DRV_DoneIRQHandler
 .text.FLASH_DRV_ReadCollisionIRQHandler
                0x00003b44       0x20 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
                0x00003b44                FLASH_DRV_ReadCollisionIRQHandler
 .text.EFM_IRQHandler
                0x00003b64       0x1c libGENERATED_SDK_TARGET.a(flash_driver.c.o)
                0x00003b64                EFM_IRQHandler
 .text.EFM_Error_IRQHandler
                0x00003b80       0x1c libGENERATED_SDK_TARGET.a(flash_driver.c.o)
                0x00003b80                EFM_Error_IRQHandler
 .text.FLASH_DRV_EnableCmdCompleteInterrupt
                0x00003b9c       0x10 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
                0x00003b9c                FLASH_DRV_EnableCmdCompleteInterrupt
 .text.FLASH_DRV_EnableReadCollisionInterrupt
                0x00003bac       0x10 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
                0x00003bac                FLASH_DRV_EnableReadCollisionInterrupt
 .text.FLASH_DRV_Init
                0x00003bbc       0x6c libGENERATED_SDK_TARGET.a(flash_driver.c.o)
                0x00003bbc                FLASH_DRV_Init
 .text.SystemInit
                0x00003c28       0x94 libGENERATED_SDK_TARGET.a(system_YTM32B1MD1.c.o)
                0x00003c28                SystemInit
 .text.lin_set_flag
                0x00003cbc       0x70 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.lin_get_frame_by_id
                0x00003d2c       0x58 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.lin_error_handle
                0x00003d84       0x34 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.lin_copy_rx_frame
                0x00003db8      0x198 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.lin_id_to_pid
                0x00003f50        0xa libGENERATED_SDK_TARGET.a(lin_core.c.o)
 *fill*         0x00003f5a        0x2 
 .text.lin_update_ifc_status
                0x00003f5c       0xfc libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .text.l_lfx_tx
                0x00004058      0x3e8 libGENERATED_SDK_TARGET.a(lin_core.c.o)
                0x00004058                l_lfx_tx
 .text.l_lfx_rx
                0x00004440      0x190 libGENERATED_SDK_TARGET.a(lin_core.c.o)
                0x00004440                l_lfx_rx
 .text.lin_frame_rx_handler
                0x000045d0      0x318 libGENERATED_SDK_TARGET.a(lin_core.c.o)
                0x000045d0                lin_frame_rx_handler
 .text.l_sys_irq_disable
                0x000048e8        0xa libGENERATED_SDK_TARGET.a(lin_core.c.o)
                0x000048e8                l_sys_irq_disable
 .text.l_sys_irq_restore
                0x000048f2        0x8 libGENERATED_SDK_TARGET.a(lin_core.c.o)
                0x000048f2                l_sys_irq_restore
 *fill*         0x000048fa        0x2 
 .text.l_sys_init
                0x000048fc       0x98 libGENERATED_SDK_TARGET.a(lin_core.c.o)
                0x000048fc                l_sys_init
 .text.l_ifc_init
                0x00004994       0x1c libGENERATED_SDK_TARGET.a(lin_core.c.o)
                0x00004994                l_ifc_init
 .text.lin_put_raw
                0x000049b0       0xd8 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text.lin_init_queue
                0x00004a88       0x58 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text.ld_put_raw
                0x00004ae0       0xbc libGENERATED_SDK_TARGET.a(lin_tp.c.o)
                0x00004ae0                ld_put_raw
 .text.lin_make_slave_response_pdu
                0x00004b9c      0x1ac libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text.lin_check_response
                0x00004d48      0x378 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text.ld_get_raw
                0x000050c0       0x70 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
                0x000050c0                ld_get_raw
 .text.lin_receive_message
                0x00005130      0x130 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .text.ld_init  0x00005260       0xa8 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
                0x00005260                ld_init
 .text.lin_tp_handle
                0x00005308      0x650 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
                0x00005308                lin_tp_handle
 .text.ld_send_message
                0x00005958      0x1e8 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
                0x00005958                ld_send_message
 .text.ld_receive_message
                0x00005b40       0x44 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
                0x00005b40                ld_receive_message
 .text.ld_tx_status
                0x00005b84       0x14 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
                0x00005b84                ld_tx_status
 .text.ld_rx_status
                0x00005b98       0x14 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
                0x00005b98                ld_rx_status
 .text.__NVIC_SystemReset
                0x00005bac       0x24 libGENERATED_SDK_TARGET.a(uds.c.o)
 .text.Uds_RestartS3Server
                0x00005bd0       0x30 libGENERATED_SDK_TARGET.a(uds.c.o)
 .text.Uds_SendData
                0x00005c00       0x74 libGENERATED_SDK_TARGET.a(uds.c.o)
 .text.Uds_Init
                0x00005c74       0x54 libGENERATED_SDK_TARGET.a(uds.c.o)
                0x00005c74                Uds_Init
 .text.Uds_NoResponse
                0x00005cc8       0x3c libGENERATED_SDK_TARGET.a(uds.c.o)
                0x00005cc8                Uds_NoResponse
 .text.Uds_SendNegativeResponse
                0x00005d04       0xc0 libGENERATED_SDK_TARGET.a(uds.c.o)
                0x00005d04                Uds_SendNegativeResponse
 .text.Uds_SendPositiveResponse
                0x00005dc4       0x8c libGENERATED_SDK_TARGET.a(uds.c.o)
                0x00005dc4                Uds_SendPositiveResponse
 .text.Uds_SetSession
                0x00005e50       0x4c libGENERATED_SDK_TARGET.a(uds.c.o)
                0x00005e50                Uds_SetSession
 .text.Uds_GetSession
                0x00005e9c       0x2c libGENERATED_SDK_TARGET.a(uds.c.o)
                0x00005e9c                Uds_GetSession
 .text.Uds_TimeService
                0x00005ec8       0x30 libGENERATED_SDK_TARGET.a(uds.c.o)
                0x00005ec8                Uds_TimeService
 .text.Uds_SetupTmpParam
                0x00005ef8       0x24 libGENERATED_SDK_TARGET.a(uds.c.o)
                0x00005ef8                Uds_SetupTmpParam
 .text.Uds_SetSecureLevel
                0x00005f1c       0x20 libGENERATED_SDK_TARGET.a(uds.c.o)
                0x00005f1c                Uds_SetSecureLevel
 .text.Uds_MainFunction
                0x00005f3c      0x1f0 libGENERATED_SDK_TARGET.a(uds.c.o)
                0x00005f3c                Uds_MainFunction
 .text.SysTick_Handler
                0x0000612c       0x10 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
                0x0000612c                SysTick_Handler
 .text.OSIF_SemaPost
                0x0000613c       0x26 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
                0x0000613c                OSIF_SemaPost
 .text.OSIF_SemaCreate
                0x00006162       0x14 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
                0x00006162                OSIF_SemaCreate
 .text.OSIF_SemaDestroy
                0x00006176        0x4 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
                0x00006176                OSIF_SemaDestroy
 *fill*         0x0000617a        0x2 
 .text.srand    0x0000617c       0x10 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-rand.o)
                0x0000617c                srand
 .text.rand     0x0000618c       0x38 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-rand.o)
                0x0000618c                rand
 *fill*         0x000061c4        0x4 
 .text.rand.__stub
                0x000061c8       0x10 linker stubs
                0x000061d8                text_region_end = .
                0x000061d8                TEXT_end = .

.ARM            0x000061d8        0x8
                0x000061d8                ARM_start = .
                0x000061d8                ARM.exidx_region_start = .
 *(.ARM.exidx)
 .ARM.exidx     0x000061d8        0x8 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
 *(.ARM.exidx*)
                0x000061e0                ARM.exidx_region_end = .
                0x000061e0                ARM_end = .
                0x000061e0                CODE_RAM_rom_start_not_align = .
                0x000061e0                CODE_RAM_rom_start = (CODE_RAM_rom_start_not_align + (CODE_RAM_rom_start_not_align % 0x4))
                0x000062b8                CODE_RAM_rom_end = ((CODE_RAM_rom_start + CODE_RAM_ram_end) - CODE_RAM_ram_start)
                0x000062b8                DATA_RAM_rom_start_not_align = CODE_RAM_rom_end
                0x000062b8                DATA_RAM_rom_start = (DATA_RAM_rom_start_not_align + (DATA_RAM_rom_start_not_align % 0x4))
                0x000067d8                DATA_RAM_rom_end = ((DATA_RAM_rom_start + DATA_RAM_ram_end) - DATA_RAM_ram_start)

.IVT_RAM        0x1fff8000      0x400
                0x1fff8000                . = ALIGN (0x400)
                0x1fff8000                IVT_RAM_start = .
                0x1fff8400                . = (. + 0x400)
 *fill*         0x1fff8000      0x400 
                0x1fff8400                IVT_RAM_end = .

.FBL_VAR        0x1fff8400        0x8
                0x1fff8400                . = ALIGN (0x8)
                0x1fff8400                FBL_VAR_start = .
                0x1fff8400                fbl_bss_region_start = .
 *(.fbl_bss)
 .fbl_bss       0x1fff8400        0x4 CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
                0x1fff8400                KeepInBootVar
                0x1fff8404                fbl_bss_region_end = .
                0x1fff8408                . = ALIGN (0x8)
 *fill*         0x1fff8404        0x4 
                0x1fff8408                FBL_VAR_end = .

.BSS            0x1fff8410      0x7b0
                0x1fff8410                BSS_start = .
                0x1fff8410                bss_region_start = .
 *(.bss)
 *(.bss*)
 .bss.DCM_m_t_PollingTimer
                0x1fff8410        0x4 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
                0x1fff8410                DCM_m_t_PollingTimer
 .bss.DCM_m_t_StayInTimer
                0x1fff8414        0x4 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
                0x1fff8414                DCM_m_t_StayInTimer
 .bss.ECU_m_num_SysTick
                0x1fff8418        0x4 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
                0x1fff8418                ECU_m_num_SysTick
 .bss.attemptsCnt.4
                0x1fff841c        0x1 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 *fill*         0x1fff841d        0x3 
 .bss.eraseOffset.1
                0x1fff8420        0x4 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .bss.keyBuf.2  0x1fff8424        0x4 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .bss.resetCommand
                0x1fff8428        0x1 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
                0x1fff8428                resetCommand
 *fill*         0x1fff8429        0x3 
 .bss.seedBuf.5
                0x1fff842c        0x4 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .bss.seedState.3
                0x1fff8430        0x1 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 *fill*         0x1fff8431        0x3 
 .bss.udState   0x1fff8434       0x10 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .bss.writeOffset.0
                0x1fff8444        0x4 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .bss.appEntry  0x1fff8448        0x4 CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
                0x1fff8448                appEntry
 .bss.appStack  0x1fff844c        0x4 CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
                0x1fff844c                appStack
 .bss.g_clockManCallbacksArr
                0x1fff8450        0x4 libGENERATED_CONFIG_TARGET.a(clock_config.c.o)
                0x1fff8450                g_clockManCallbacksArr
 .bss.LinConfig_0_flag_pool
                0x1fff8454        0x2 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 *fill*         0x1fff8456        0x2 
 .bss.LinConfig_0_frame_flag_pool
                0x1fff8458        0x1 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 *fill*         0x1fff8459        0x3 
 .bss.LinConfig_0_slave_state
                0x1fff845c        0x1 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 *fill*         0x1fff845d        0x3 
 .bss.LinConfig_0_tp_rx_queue
                0x1fff8460       0xf0 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .bss.LinConfig_0_tp_state
                0x1fff8550       0x34 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .bss.LinConfig_0_tp_tx_queue
                0x1fff8584       0xf0 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .bss.LinConfig_0_update_pool
                0x1fff8674        0x2 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 *fill*         0x1fff8676        0x2 
 .bss.g_linGlobalState
                0x1fff8678       0x4c libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
                0x1fff8678                g_linGlobalState
 .bss.linflexd_lin_config0_State
                0x1fff86c4       0x20 libGENERATED_CONFIG_TARGET.a(linflexd_lin_config.c.o)
                0x1fff86c4                linflexd_lin_config0_State
 .bss.flash_config0
                0x1fff86e4        0x8 libGENERATED_CONFIG_TARGET.a(flash_config.c.o)
                0x1fff86e4                flash_config0
 .bss.flash_config0_State
                0x1fff86ec       0x2c libGENERATED_CONFIG_TARGET.a(flash_config.c.o)
                0x1fff86ec                flash_config0_State
 .bss.uds_receivedData
                0x1fff8718        0x4 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
                0x1fff8718                uds_receivedData
 .bss.uds_receivedData0
                0x1fff871c      0x200 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
                0x1fff871c                uds_receivedData0
 .bss.uds_transmitData
                0x1fff891c        0x4 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
                0x1fff891c                uds_transmitData
 .bss.uds_transmitData0
                0x1fff8920      0x200 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
                0x1fff8920                uds_transmitData0
 .bss.g_clockState
                0x1fff8b20       0x10 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .bss.g_fxoscClkFreq
                0x1fff8b30        0x4 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .bss.g_interruptDisableCount
                0x1fff8b34        0x4 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
 .bss.s_virtEdmaState
                0x1fff8b38        0x4 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .bss.s_LINFlexDStatePtr
                0x1fff8b3c        0xc libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .bss.s_LINFlexDUserconfigPtr
                0x1fff8b48        0xc libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .bss.s_ptmrSourceClockFrequency
                0x1fff8b54        0x4 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .bss.s_lptmrClkFreq
                0x1fff8b58        0x4 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .bss.s_hcuStatePtr
                0x1fff8b5c        0x4 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .bss.trngCfgSetting
                0x1fff8b60       0x28 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
                0x1fff8b60                trngCfgSetting
 .bss.s_FlashStatePtr
                0x1fff8b88        0x4 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .bss.uds_state
                0x1fff8b8c       0x30 libGENERATED_SDK_TARGET.a(uds.c.o)
 .bss.s_osif_tick_cnt
                0x1fff8bbc        0x4 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
                0x1fff8bc0                bss_region_end = .
                0x1fff8bc0                BSS_end = .

.CODE_RAM       0x1fff8bc0       0xd8 load address 0x000061e0
                0x1fff8bc0                . = ALIGN (0x4)
                0x1fff8bc0                CODE_RAM_ram_start = .
                0x1fff8bc0                CODE_RAM_start = .
                0x1fff8bc0                code_ram_region_start = .
 *(.code_ram)
 .code_ram      0x1fff8bc0       0xd8 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
                0x1fff8bc0                FLASH_LaunchCommandSequence
                0x1fff8c98                code_ram_region_end = .
                0x1fff8c98                CODE_RAM_end = .
                0x1fff8c98                CODE_RAM_ram_end = .
                0x00000001                ASSERT (((CODE_RAM_ram_end - CODE_RAM_ram_start) == (CODE_RAM_rom_end - CODE_RAM_rom_start)), Copy Section CODE_RAM Size non-aligned)

.glue_7         0x1fff8c98        0x0 load address 0x000062b8
 .glue_7        0x1fff8c98        0x0 linker stubs

.glue_7t        0x1fff8c98        0x0 load address 0x000062b8
 .glue_7t       0x1fff8c98        0x0 linker stubs

.vfp11_veneer   0x1fff8c98        0x0 load address 0x000062b8
 .vfp11_veneer  0x1fff8c98        0x0 linker stubs

.v4_bx          0x1fff8c98        0x0 load address 0x000062b8
 .v4_bx         0x1fff8c98        0x0 linker stubs

.iplt           0x1fff8c98        0x0 load address 0x000062b8
 .iplt          0x1fff8c98        0x0 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o

.igot.plt       0x1fff8c98        0x0 load address 0x000062b8
 .igot.plt      0x1fff8c98        0x0 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o

.rel.dyn        0x1fff8c98        0x0 load address 0x000062b8
 .rel.iplt      0x1fff8c98        0x0 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o

.DATA_RAM       0x1fff8c98      0x520 load address 0x000062b8
                0x1fff8c98                DATA_RAM_ram_start = .
                0x1fff8c98                DATA_RAM_start = .
                0x1fff8c98                data_region_start = .
 *(.data)
 *(.data*)
 .data.uds_global_ip_api
                0x1fff8c98       0x18 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
                0x1fff8c98                uds_global_ip_api
 .data.clock_config0ClockManager
                0x1fff8cb0       0x10 libGENERATED_CONFIG_TARGET.a(clock_config.c.o)
                0x1fff8cb0                clock_config0ClockManager
 .data.clock_config0PeripheralClockConfig
                0x1fff8cc0       0x1c libGENERATED_CONFIG_TARGET.a(clock_config.c.o)
                0x1fff8cc0                clock_config0PeripheralClockConfig
 .data.g_clockManConfigsArr
                0x1fff8cdc        0x4 libGENERATED_CONFIG_TARGET.a(clock_config.c.o)
                0x1fff8cdc                g_clockManConfigsArr
 .data.g_pin_mux_InitConfigArr0
                0x1fff8ce0       0x70 libGENERATED_CONFIG_TARGET.a(pin_mux.c.o)
                0x1fff8ce0                g_pin_mux_InitConfigArr0
 .data.LinConfig_0_data_pool
                0x1fff8d50        0xd libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 *fill*         0x1fff8d5d        0x3 
 .data.LinConfig_0_id_list
                0x1fff8d60        0x6 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 *fill*         0x1fff8d66        0x2 
 .data.g_linGlobalConfig
                0x1fff8d68        0x4 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
                0x1fff8d68                g_linGlobalConfig
 .data.linflexd_lin_config0
                0x1fff8d6c       0x18 libGENERATED_CONFIG_TARGET.a(linflexd_lin_config.c.o)
                0x1fff8d6c                linflexd_lin_config0
 .data.SystemCoreClock
                0x1fff8d84        0x4 libGENERATED_SDK_TARGET.a(system_YTM32B1MD1.c.o)
                0x1fff8d84                SystemCoreClock
 .data._impure_ptr
                0x1fff8d88        0x4 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-impure.o)
                0x1fff8d88                _impure_ptr
 *fill*         0x1fff8d8c        0x4 
 .data.impure_data
                0x1fff8d90      0x428 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-impure.o)
                0x1fff91b8                data_region_end = .
                0x1fff91b8                DATA_RAM_end = .
                0x1fff91b8                DATA_RAM_ram_end = .
                0x00000001                ASSERT (((DATA_RAM_ram_end - DATA_RAM_ram_start) == (DATA_RAM_rom_end - DATA_RAM_rom_start)), Copy Section DATA_RAM Size non-aligned)

.STACK          0x20007c00      0x400
                0x20007c00                STACK_start = .
                0x20008000                . = (. + 0x400)
 *fill*         0x20007c00      0x400 
                0x20008000                STACK_end = .
                0x00000000                IVT_memory_start = ORIGIN (IVT)
                0x00000340                IVT_memory_end = (ORIGIN (IVT) + LENGTH (IVT))
                0x00000340                IVT_memory_size = LENGTH (IVT)
                0x00000340                BOOT_memory_start = ORIGIN (BOOT)
                0x00080000                BOOT_memory_end = (ORIGIN (BOOT) + LENGTH (BOOT))
                0x0007fcc0                BOOT_memory_size = LENGTH (BOOT)
                0x1fff8000                IVT_RAM_memory_start = ORIGIN (IVT_RAM)
                0x1fff8400                IVT_RAM_memory_end = (ORIGIN (IVT_RAM) + LENGTH (IVT_RAM))
                0x00000400                IVT_RAM_memory_size = LENGTH (IVT_RAM)
                0x1fff8400                FBL_VAR_memory_start = ORIGIN (FBL_VAR)
                0x1fff8410                FBL_VAR_memory_end = (ORIGIN (FBL_VAR) + LENGTH (FBL_VAR))
                0x00000010                FBL_VAR_memory_size = LENGTH (FBL_VAR)
                0x1fff8410                RAM_memory_start = ORIGIN (RAM)
                0x20007c00                RAM_memory_end = (ORIGIN (RAM) + LENGTH (RAM))
                0x0000f7f0                RAM_memory_size = LENGTH (RAM)
                0x20007c00                STACK_memory_start = ORIGIN (STACK)
                0x20008000                STACK_memory_end = (ORIGIN (STACK) + LENGTH (STACK))
                0x00000400                STACK_memory_size = LENGTH (STACK)
OUTPUT(uds_lin_bootloader.elf elf32-littlearm)
LOAD linker stubs

.debug_info     0x00000000    0x2972b
 .debug_info    0x00000000     0x1f37 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
 .debug_info    0x00001f37      0x14e CMakeFiles/uds_lin_bootloader.elf.dir/app/GenerateKeyExImpl.c.o
 .debug_info    0x00002085     0x1c91 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .debug_info    0x00003d16      0xfe0 CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
 .debug_info    0x00004cf6      0xbfe libGENERATED_CONFIG_TARGET.a(clock_config.c.o)
 .debug_info    0x000058f4      0x416 libGENERATED_CONFIG_TARGET.a(pin_mux.c.o)
 .debug_info    0x00005d0a      0xf32 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .debug_info    0x00006c3c       0x26 libGENERATED_CONFIG_TARGET.a(startup.S.o)
 .debug_info    0x00006c62      0x97a libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .debug_info    0x000075dc       0x26 libGENERATED_CONFIG_TARGET.a(RamInit0.S.o)
 .debug_info    0x00007602      0x391 libGENERATED_CONFIG_TARGET.a(RamInit1.c.o)
 .debug_info    0x00007993       0x38 libGENERATED_CONFIG_TARGET.a(RamInit2.c.o)
 .debug_info    0x000079cb      0x61d libGENERATED_CONFIG_TARGET.a(interrupt_config.c.o)
 .debug_info    0x00007fe8      0x48f libGENERATED_CONFIG_TARGET.a(linflexd_lin_config.c.o)
 .debug_info    0x00008477      0x15c libGENERATED_CONFIG_TARGET.a(ptmr_config.c.o)
 .debug_info    0x000085d3      0x291 libGENERATED_CONFIG_TARGET.a(lptmr_config.c.o)
 .debug_info    0x00008864      0x24a libGENERATED_CONFIG_TARGET.a(flash_config.c.o)
 .debug_info    0x00008aae      0x505 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 .debug_info    0x00008fb3     0x3463 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .debug_info    0x0000c416     0x14b8 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .debug_info    0x0000d8ce      0x5c8 libGENERATED_SDK_TARGET.a(pins_port_hw_access.c.o)
 .debug_info    0x0000de96      0xe60 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
 .debug_info    0x0000ecf6     0x3d91 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .debug_info    0x00012a87      0x9d8 libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .debug_info    0x0001345f      0x66f libGENERATED_SDK_TARGET.a(dma_irq.c.o)
 .debug_info    0x00013ace     0x3687 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .debug_info    0x00017155      0x2c4 libGENERATED_SDK_TARGET.a(linflexd_lin_irq.c.o)
 .debug_info    0x00017419     0x1ad3 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .debug_info    0x00018eec     0x2272 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .debug_info    0x0001b15e      0x155 libGENERATED_SDK_TARGET.a(lptmr_hw_access.c.o)
 .debug_info    0x0001b2b3     0x25d2 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .debug_info    0x0001d885       0xae libGENERATED_SDK_TARGET.a(hcu_irq.c.o)
 .debug_info    0x0001d933      0x8e3 libGENERATED_SDK_TARGET.a(trng_driver.c.o)
 .debug_info    0x0001e216      0x82b libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
 .debug_info    0x0001ea41     0x28df libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .debug_info    0x00021320      0xdf5 libGENERATED_SDK_TARGET.a(system_YTM32B1MD1.c.o)
 .debug_info    0x00022115     0x2d8e libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .debug_info    0x00024ea3     0x28d9 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .debug_info    0x0002777c     0x148c libGENERATED_SDK_TARGET.a(uds.c.o)
 .debug_info    0x00028c08      0xb23 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)

.debug_abbrev   0x00000000     0x58a3
 .debug_abbrev  0x00000000      0x287 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
 .debug_abbrev  0x00000287       0xba CMakeFiles/uds_lin_bootloader.elf.dir/app/GenerateKeyExImpl.c.o
 .debug_abbrev  0x00000341      0x3ce CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .debug_abbrev  0x0000070f      0x2a5 CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
 .debug_abbrev  0x000009b4      0x142 libGENERATED_CONFIG_TARGET.a(clock_config.c.o)
 .debug_abbrev  0x00000af6      0x11c libGENERATED_CONFIG_TARGET.a(pin_mux.c.o)
 .debug_abbrev  0x00000c12      0x17c libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .debug_abbrev  0x00000d8e       0x14 libGENERATED_CONFIG_TARGET.a(startup.S.o)
 .debug_abbrev  0x00000da2      0x14d libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .debug_abbrev  0x00000eef       0x14 libGENERATED_CONFIG_TARGET.a(RamInit0.S.o)
 .debug_abbrev  0x00000f03      0x187 libGENERATED_CONFIG_TARGET.a(RamInit1.c.o)
 .debug_abbrev  0x0000108a       0x2e libGENERATED_CONFIG_TARGET.a(RamInit2.c.o)
 .debug_abbrev  0x000010b8       0xa2 libGENERATED_CONFIG_TARGET.a(interrupt_config.c.o)
 .debug_abbrev  0x0000115a       0xdd libGENERATED_CONFIG_TARGET.a(linflexd_lin_config.c.o)
 .debug_abbrev  0x00001237       0x9a libGENERATED_CONFIG_TARGET.a(ptmr_config.c.o)
 .debug_abbrev  0x000012d1       0x9a libGENERATED_CONFIG_TARGET.a(lptmr_config.c.o)
 .debug_abbrev  0x0000136b       0xb3 libGENERATED_CONFIG_TARGET.a(flash_config.c.o)
 .debug_abbrev  0x0000141e      0x128 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 .debug_abbrev  0x00001546      0x4b1 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .debug_abbrev  0x000019f7      0x35f libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .debug_abbrev  0x00001d56      0x219 libGENERATED_SDK_TARGET.a(pins_port_hw_access.c.o)
 .debug_abbrev  0x00001f6f      0x224 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
 .debug_abbrev  0x00002193      0x47d libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .debug_abbrev  0x00002610      0x239 libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .debug_abbrev  0x00002849      0x1cd libGENERATED_SDK_TARGET.a(dma_irq.c.o)
 .debug_abbrev  0x00002a16      0x4a0 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .debug_abbrev  0x00002eb6       0xe6 libGENERATED_SDK_TARGET.a(linflexd_lin_irq.c.o)
 .debug_abbrev  0x00002f9c      0x446 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .debug_abbrev  0x000033e2      0x3a1 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .debug_abbrev  0x00003783       0xd1 libGENERATED_SDK_TARGET.a(lptmr_hw_access.c.o)
 .debug_abbrev  0x00003854      0x4f0 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .debug_abbrev  0x00003d44       0x64 libGENERATED_SDK_TARGET.a(hcu_irq.c.o)
 .debug_abbrev  0x00003da8      0x1c4 libGENERATED_SDK_TARGET.a(trng_driver.c.o)
 .debug_abbrev  0x00003f6c      0x196 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
 .debug_abbrev  0x00004102      0x469 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .debug_abbrev  0x0000456b      0x2f5 libGENERATED_SDK_TARGET.a(system_YTM32B1MD1.c.o)
 .debug_abbrev  0x00004860      0x505 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .debug_abbrev  0x00004d65      0x45b libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .debug_abbrev  0x000051c0      0x3d9 libGENERATED_SDK_TARGET.a(uds.c.o)
 .debug_abbrev  0x00005599      0x30a libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)

.debug_loc      0x00000000    0x16472
 .debug_loc     0x00000000       0x25 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
 .debug_loc     0x00000025      0x11e CMakeFiles/uds_lin_bootloader.elf.dir/app/GenerateKeyExImpl.c.o
 .debug_loc     0x00000143     0x1cf9 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .debug_loc     0x00001e3c       0xed CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
 .debug_loc     0x00001f29       0x38 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .debug_loc     0x00001f61      0x1a8 libGENERATED_CONFIG_TARGET.a(RamInit1.c.o)
 .debug_loc     0x00002109     0x2621 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .debug_loc     0x0000472a      0x8f2 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .debug_loc     0x0000501c      0x1bf libGENERATED_SDK_TARGET.a(pins_port_hw_access.c.o)
 .debug_loc     0x000051db      0x2f6 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
 .debug_loc     0x000054d1     0x2c77 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .debug_loc     0x00008148      0x8b4 libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .debug_loc     0x000089fc       0x6f libGENERATED_SDK_TARGET.a(dma_irq.c.o)
 .debug_loc     0x00008a6b     0x25e1 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .debug_loc     0x0000b04c      0xbfc libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .debug_loc     0x0000bc48     0x186a libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .debug_loc     0x0000d4b2       0x23 libGENERATED_SDK_TARGET.a(lptmr_hw_access.c.o)
 .debug_loc     0x0000d4d5     0x12d4 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .debug_loc     0x0000e7a9      0x102 libGENERATED_SDK_TARGET.a(trng_driver.c.o)
 .debug_loc     0x0000e8ab      0x1a7 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
 .debug_loc     0x0000ea52     0x1b98 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .debug_loc     0x000105ea      0x230 libGENERATED_SDK_TARGET.a(system_YTM32B1MD1.c.o)
 .debug_loc     0x0001081a     0x2447 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .debug_loc     0x00012c61     0x27b3 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .debug_loc     0x00015414      0xd34 libGENERATED_SDK_TARGET.a(uds.c.o)
 .debug_loc     0x00016148      0x32a libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)

.debug_aranges  0x00000000     0x12d8
 .debug_aranges
                0x00000000       0x38 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
 .debug_aranges
                0x00000038       0x20 CMakeFiles/uds_lin_bootloader.elf.dir/app/GenerateKeyExImpl.c.o
 .debug_aranges
                0x00000058       0x90 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .debug_aranges
                0x000000e8       0x58 CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
 .debug_aranges
                0x00000140       0x18 libGENERATED_CONFIG_TARGET.a(clock_config.c.o)
 .debug_aranges
                0x00000158       0x18 libGENERATED_CONFIG_TARGET.a(pin_mux.c.o)
 .debug_aranges
                0x00000170       0x20 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .debug_aranges
                0x00000190       0x20 libGENERATED_CONFIG_TARGET.a(startup.S.o)
 .debug_aranges
                0x000001b0      0x398 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .debug_aranges
                0x00000548       0x20 libGENERATED_CONFIG_TARGET.a(RamInit0.S.o)
 .debug_aranges
                0x00000568       0x20 libGENERATED_CONFIG_TARGET.a(RamInit1.c.o)
 .debug_aranges
                0x00000588       0x20 libGENERATED_CONFIG_TARGET.a(RamInit2.c.o)
 .debug_aranges
                0x000005a8       0x20 libGENERATED_CONFIG_TARGET.a(interrupt_config.c.o)
 .debug_aranges
                0x000005c8       0x18 libGENERATED_CONFIG_TARGET.a(linflexd_lin_config.c.o)
 .debug_aranges
                0x000005e0       0x18 libGENERATED_CONFIG_TARGET.a(ptmr_config.c.o)
 .debug_aranges
                0x000005f8       0x18 libGENERATED_CONFIG_TARGET.a(lptmr_config.c.o)
 .debug_aranges
                0x00000610       0x18 libGENERATED_CONFIG_TARGET.a(flash_config.c.o)
 .debug_aranges
                0x00000628       0x20 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 .debug_aranges
                0x00000648       0x80 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .debug_aranges
                0x000006c8       0xd0 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .debug_aranges
                0x00000798       0x28 libGENERATED_SDK_TARGET.a(pins_port_hw_access.c.o)
 .debug_aranges
                0x000007c0       0x68 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
 .debug_aranges
                0x00000828      0x138 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .debug_aranges
                0x00000960       0x88 libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .debug_aranges
                0x000009e8       0xa0 libGENERATED_SDK_TARGET.a(dma_irq.c.o)
 .debug_aranges
                0x00000a88       0xd8 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .debug_aranges
                0x00000b60       0x30 libGENERATED_SDK_TARGET.a(linflexd_lin_irq.c.o)
 .debug_aranges
                0x00000b90       0xa0 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .debug_aranges
                0x00000c30       0xa8 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .debug_aranges
                0x00000cd8       0x20 libGENERATED_SDK_TARGET.a(lptmr_hw_access.c.o)
 .debug_aranges
                0x00000cf8       0xa8 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .debug_aranges
                0x00000da0       0x20 libGENERATED_SDK_TARGET.a(hcu_irq.c.o)
 .debug_aranges
                0x00000dc0       0x40 libGENERATED_SDK_TARGET.a(trng_driver.c.o)
 .debug_aranges
                0x00000e00       0x88 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
 .debug_aranges
                0x00000e88      0x120 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .debug_aranges
                0x00000fa8       0x30 libGENERATED_SDK_TARGET.a(system_YTM32B1MD1.c.o)
 .debug_aranges
                0x00000fd8      0x118 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .debug_aranges
                0x000010f0       0xf0 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .debug_aranges
                0x000011e0       0x88 libGENERATED_SDK_TARGET.a(uds.c.o)
 .debug_aranges
                0x00001268       0x70 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)

.debug_ranges   0x00000000     0x19b0
 .debug_ranges  0x00000000       0x28 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
 .debug_ranges  0x00000028       0x10 CMakeFiles/uds_lin_bootloader.elf.dir/app/GenerateKeyExImpl.c.o
 .debug_ranges  0x00000038      0x198 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .debug_ranges  0x000001d0       0x48 CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
 .debug_ranges  0x00000218       0x10 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .debug_ranges  0x00000228      0x388 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .debug_ranges  0x000005b0       0x10 libGENERATED_CONFIG_TARGET.a(RamInit1.c.o)
 .debug_ranges  0x000005c0       0x10 libGENERATED_CONFIG_TARGET.a(RamInit2.c.o)
 .debug_ranges  0x000005d0       0x10 libGENERATED_CONFIG_TARGET.a(interrupt_config.c.o)
 .debug_ranges  0x000005e0       0x10 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 .debug_ranges  0x000005f0      0x1b0 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .debug_ranges  0x000007a0       0xd8 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .debug_ranges  0x00000878       0x18 libGENERATED_SDK_TARGET.a(pins_port_hw_access.c.o)
 .debug_ranges  0x00000890       0x58 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
 .debug_ranges  0x000008e8      0x170 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .debug_ranges  0x00000a58       0x78 libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .debug_ranges  0x00000ad0       0x90 libGENERATED_SDK_TARGET.a(dma_irq.c.o)
 .debug_ranges  0x00000b60      0x190 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .debug_ranges  0x00000cf0       0x20 libGENERATED_SDK_TARGET.a(linflexd_lin_irq.c.o)
 .debug_ranges  0x00000d10       0xa8 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .debug_ranges  0x00000db8      0x120 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .debug_ranges  0x00000ed8       0x10 libGENERATED_SDK_TARGET.a(lptmr_hw_access.c.o)
 .debug_ranges  0x00000ee8      0x130 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .debug_ranges  0x00001018       0x10 libGENERATED_SDK_TARGET.a(hcu_irq.c.o)
 .debug_ranges  0x00001028       0x30 libGENERATED_SDK_TARGET.a(trng_driver.c.o)
 .debug_ranges  0x00001058       0x78 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
 .debug_ranges  0x000010d0      0x188 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .debug_ranges  0x00001258       0x70 libGENERATED_SDK_TARGET.a(system_YTM32B1MD1.c.o)
 .debug_ranges  0x000012c8      0x3c0 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .debug_ranges  0x00001688      0x1a0 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .debug_ranges  0x00001828       0xe0 libGENERATED_SDK_TARGET.a(uds.c.o)
 .debug_ranges  0x00001908       0xa8 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)

.debug_line     0x00000000    0x14d21
 .debug_line    0x00000000      0x665 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
 .debug_line    0x00000665       0xfb CMakeFiles/uds_lin_bootloader.elf.dir/app/GenerateKeyExImpl.c.o
 .debug_line    0x00000760     0x10b4 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .debug_line    0x00001814      0x61c CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
 .debug_line    0x00001e30      0x2d8 libGENERATED_CONFIG_TARGET.a(clock_config.c.o)
 .debug_line    0x00002108      0x254 libGENERATED_CONFIG_TARGET.a(pin_mux.c.o)
 .debug_line    0x0000235c      0x2b2 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .debug_line    0x0000260e       0xa0 libGENERATED_CONFIG_TARGET.a(startup.S.o)
 .debug_line    0x000026ae      0xee0 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .debug_line    0x0000358e       0xc0 libGENERATED_CONFIG_TARGET.a(RamInit0.S.o)
 .debug_line    0x0000364e      0x3ee libGENERATED_CONFIG_TARGET.a(RamInit1.c.o)
 .debug_line    0x00003a3c       0x81 libGENERATED_CONFIG_TARGET.a(RamInit2.c.o)
 .debug_line    0x00003abd      0x178 libGENERATED_CONFIG_TARGET.a(interrupt_config.c.o)
 .debug_line    0x00003c35      0x254 libGENERATED_CONFIG_TARGET.a(linflexd_lin_config.c.o)
 .debug_line    0x00003e89      0x1e8 libGENERATED_CONFIG_TARGET.a(ptmr_config.c.o)
 .debug_line    0x00004071      0x1eb libGENERATED_CONFIG_TARGET.a(lptmr_config.c.o)
 .debug_line    0x0000425c      0x1eb libGENERATED_CONFIG_TARGET.a(flash_config.c.o)
 .debug_line    0x00004447      0x26e libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 .debug_line    0x000046b5     0x17fb libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .debug_line    0x00005eb0      0x7ec libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .debug_line    0x0000669c      0x459 libGENERATED_SDK_TARGET.a(pins_port_hw_access.c.o)
 .debug_line    0x00006af5      0x58e libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
 .debug_line    0x00007083     0x1436 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .debug_line    0x000084b9      0x79c libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .debug_line    0x00008c55      0x40c libGENERATED_SDK_TARGET.a(dma_irq.c.o)
 .debug_line    0x00009061     0x1700 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .debug_line    0x0000a761      0x2d5 libGENERATED_SDK_TARGET.a(linflexd_lin_irq.c.o)
 .debug_line    0x0000aa36      0x9dc libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .debug_line    0x0000b412      0xf8f libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .debug_line    0x0000c3a1      0x254 libGENERATED_SDK_TARGET.a(lptmr_hw_access.c.o)
 .debug_line    0x0000c5f5     0x171d libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .debug_line    0x0000dd12      0x100 libGENERATED_SDK_TARGET.a(hcu_irq.c.o)
 .debug_line    0x0000de12      0x415 libGENERATED_SDK_TARGET.a(trng_driver.c.o)
 .debug_line    0x0000e227      0x51a libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
 .debug_line    0x0000e741     0x16d3 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .debug_line    0x0000fe14      0x496 libGENERATED_SDK_TARGET.a(system_YTM32B1MD1.c.o)
 .debug_line    0x000102aa     0x1a20 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .debug_line    0x00011cca     0x1e3d libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .debug_line    0x00013b07      0xb07 libGENERATED_SDK_TARGET.a(uds.c.o)
 .debug_line    0x0001460e      0x713 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)

.debug_str      0x00000000     0xbfc3
 .debug_str     0x00000000     0x331b CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
                               0x34b1 (size before relaxing)
 .debug_str     0x0000331b      0x126 CMakeFiles/uds_lin_bootloader.elf.dir/app/GenerateKeyExImpl.c.o
                                0x22e (size before relaxing)
 .debug_str     0x00003441      0x839 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
                               0x147b (size before relaxing)
 .debug_str     0x00003c7a      0x2be CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
                               0x1464 (size before relaxing)
 .debug_str     0x00003f38      0x217 libGENERATED_CONFIG_TARGET.a(clock_config.c.o)
                               0x175c (size before relaxing)
 .debug_str     0x0000414f       0x50 libGENERATED_CONFIG_TARGET.a(pin_mux.c.o)
                                0x5e3 (size before relaxing)
 .debug_str     0x0000419f      0xc23 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
                               0x1054 (size before relaxing)
 .debug_str     0x00004dc2       0x5e libGENERATED_CONFIG_TARGET.a(startup.S.o)
                                 0xa4 (size before relaxing)
 .debug_str     0x00004e20      0x908 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
                                0xb01 (size before relaxing)
 .debug_str     0x00005728       0x51 libGENERATED_CONFIG_TARGET.a(RamInit0.S.o)
                                 0xa5 (size before relaxing)
 .debug_str     0x00005779      0x1aa libGENERATED_CONFIG_TARGET.a(RamInit1.c.o)
                                0x363 (size before relaxing)
 .debug_str     0x00005923       0x5a libGENERATED_CONFIG_TARGET.a(RamInit2.c.o)
                                0x142 (size before relaxing)
 .debug_str     0x0000597d       0x6d libGENERATED_CONFIG_TARGET.a(interrupt_config.c.o)
                                0xe2d (size before relaxing)
 .debug_str     0x000059ea       0x5c libGENERATED_CONFIG_TARGET.a(linflexd_lin_config.c.o)
                                0x917 (size before relaxing)
 .debug_str     0x00005a46       0xfd libGENERATED_CONFIG_TARGET.a(ptmr_config.c.o)
                                0x2bf (size before relaxing)
 .debug_str     0x00005b43       0x55 libGENERATED_CONFIG_TARGET.a(lptmr_config.c.o)
                                0x63d (size before relaxing)
 .debug_str     0x00005b98       0x55 libGENERATED_CONFIG_TARGET.a(flash_config.c.o)
                                0x32c (size before relaxing)
 .debug_str     0x00005bed      0x330 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
                                0x544 (size before relaxing)
 .debug_str     0x00005f1d      0x935 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
                               0x1eec (size before relaxing)
 .debug_str     0x00006852      0x4a4 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
                               0x141c (size before relaxing)
 .debug_str     0x00006cf6       0x7b libGENERATED_SDK_TARGET.a(pins_port_hw_access.c.o)
                                0x673 (size before relaxing)
 .debug_str     0x00006d71      0x217 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
                               0x1148 (size before relaxing)
 .debug_str     0x00006f88     0x14e4 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
                               0x2d60 (size before relaxing)
 .debug_str     0x0000846c       0x7d libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
                                0x76a (size before relaxing)
 .debug_str     0x000084e9       0x7d libGENERATED_SDK_TARGET.a(dma_irq.c.o)
                                0x45a (size before relaxing)
 .debug_str     0x00008566      0xde5 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
                               0x302b (size before relaxing)
 .debug_str     0x0000934b       0xbc libGENERATED_SDK_TARGET.a(linflexd_lin_irq.c.o)
                                0x4e4 (size before relaxing)
 .debug_str     0x00009407      0x417 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
                               0x1f92 (size before relaxing)
 .debug_str     0x0000981e      0x5c6 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
                               0x18d2 (size before relaxing)
 .debug_str     0x00009de4       0x6d libGENERATED_SDK_TARGET.a(lptmr_hw_access.c.o)
                                0x224 (size before relaxing)
 .debug_str     0x00009e51      0x930 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
                               0x2360 (size before relaxing)
 .debug_str     0x0000a781       0x63 libGENERATED_SDK_TARGET.a(hcu_irq.c.o)
                                0x208 (size before relaxing)
 .debug_str     0x0000a7e4      0x2f5 libGENERATED_SDK_TARGET.a(trng_driver.c.o)
                                0xebf (size before relaxing)
 .debug_str     0x0000aad9       0xcb libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
                                0xebc (size before relaxing)
 .debug_str     0x0000aba4      0x5a3 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
                               0x20d0 (size before relaxing)
 .debug_str     0x0000b147      0x1d3 libGENERATED_SDK_TARGET.a(system_YTM32B1MD1.c.o)
                                0x649 (size before relaxing)
 .debug_str     0x0000b31a      0x45c libGENERATED_SDK_TARGET.a(lin_core.c.o)
                               0x1f47 (size before relaxing)
 .debug_str     0x0000b776      0x448 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
                               0x1303 (size before relaxing)
 .debug_str     0x0000bbbe      0x25c libGENERATED_SDK_TARGET.a(uds.c.o)
                                0xb76 (size before relaxing)
 .debug_str     0x0000be1a      0x1a9 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
                               0x1055 (size before relaxing)

.comment        0x00000000       0x49
 .comment       0x00000000       0x49 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
                                 0x4a (size before relaxing)
 .comment       0x00000049       0x4a CMakeFiles/uds_lin_bootloader.elf.dir/app/GenerateKeyExImpl.c.o
 .comment       0x00000049       0x4a CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .comment       0x00000049       0x4a CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
 .comment       0x00000049       0x4a libGENERATED_CONFIG_TARGET.a(clock_config.c.o)
 .comment       0x00000049       0x4a libGENERATED_CONFIG_TARGET.a(pin_mux.c.o)
 .comment       0x00000049       0x4a libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .comment       0x00000049       0x4a libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .comment       0x00000049       0x4a libGENERATED_CONFIG_TARGET.a(RamInit1.c.o)
 .comment       0x00000049       0x4a libGENERATED_CONFIG_TARGET.a(RamInit2.c.o)
 .comment       0x00000049       0x4a libGENERATED_CONFIG_TARGET.a(interrupt_config.c.o)
 .comment       0x00000049       0x4a libGENERATED_CONFIG_TARGET.a(linflexd_lin_config.c.o)
 .comment       0x00000049       0x4a libGENERATED_CONFIG_TARGET.a(ptmr_config.c.o)
 .comment       0x00000049       0x4a libGENERATED_CONFIG_TARGET.a(lptmr_config.c.o)
 .comment       0x00000049       0x4a libGENERATED_CONFIG_TARGET.a(flash_config.c.o)
 .comment       0x00000049       0x4a libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(pins_port_hw_access.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(dma_irq.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(linflexd_lin_irq.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(lptmr_hw_access.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(hcu_irq.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(trng_driver.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(system_YTM32B1MD1.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(uds.c.o)
 .comment       0x00000049       0x4a libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)

.ARM.attributes
                0x00000000       0x32
 .ARM.attributes
                0x00000000       0x34 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
 .ARM.attributes
                0x00000034       0x34 CMakeFiles/uds_lin_bootloader.elf.dir/app/GenerateKeyExImpl.c.o
 .ARM.attributes
                0x00000068       0x34 CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .ARM.attributes
                0x0000009c       0x34 CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
 .ARM.attributes
                0x000000d0       0x34 libGENERATED_CONFIG_TARGET.a(clock_config.c.o)
 .ARM.attributes
                0x00000104       0x34 libGENERATED_CONFIG_TARGET.a(pin_mux.c.o)
 .ARM.attributes
                0x00000138       0x34 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .ARM.attributes
                0x0000016c       0x20 libGENERATED_CONFIG_TARGET.a(startup.S.o)
 .ARM.attributes
                0x0000018c       0x22 libGENERATED_CONFIG_TARGET.a(vector.S.o)
 .ARM.attributes
                0x000001ae       0x34 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .ARM.attributes
                0x000001e2       0x22 libGENERATED_CONFIG_TARGET.a(RamInit0.S.o)
 .ARM.attributes
                0x00000204       0x34 libGENERATED_CONFIG_TARGET.a(RamInit1.c.o)
 .ARM.attributes
                0x00000238       0x34 libGENERATED_CONFIG_TARGET.a(RamInit2.c.o)
 .ARM.attributes
                0x0000026c       0x34 libGENERATED_CONFIG_TARGET.a(interrupt_config.c.o)
 .ARM.attributes
                0x000002a0       0x34 libGENERATED_CONFIG_TARGET.a(linflexd_lin_config.c.o)
 .ARM.attributes
                0x000002d4       0x34 libGENERATED_CONFIG_TARGET.a(ptmr_config.c.o)
 .ARM.attributes
                0x00000308       0x34 libGENERATED_CONFIG_TARGET.a(lptmr_config.c.o)
 .ARM.attributes
                0x0000033c       0x34 libGENERATED_CONFIG_TARGET.a(flash_config.c.o)
 .ARM.attributes
                0x00000370       0x34 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 .ARM.attributes
                0x000003a4       0x34 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .ARM.attributes
                0x000003d8       0x34 libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .ARM.attributes
                0x0000040c       0x34 libGENERATED_SDK_TARGET.a(pins_port_hw_access.c.o)
 .ARM.attributes
                0x00000440       0x34 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
 .ARM.attributes
                0x00000474       0x34 libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .ARM.attributes
                0x000004a8       0x34 libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .ARM.attributes
                0x000004dc       0x34 libGENERATED_SDK_TARGET.a(dma_irq.c.o)
 .ARM.attributes
                0x00000510       0x34 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .ARM.attributes
                0x00000544       0x34 libGENERATED_SDK_TARGET.a(linflexd_lin_irq.c.o)
 .ARM.attributes
                0x00000578       0x34 libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .ARM.attributes
                0x000005ac       0x34 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .ARM.attributes
                0x000005e0       0x34 libGENERATED_SDK_TARGET.a(lptmr_hw_access.c.o)
 .ARM.attributes
                0x00000614       0x34 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .ARM.attributes
                0x00000648       0x34 libGENERATED_SDK_TARGET.a(hcu_irq.c.o)
 .ARM.attributes
                0x0000067c       0x34 libGENERATED_SDK_TARGET.a(trng_driver.c.o)
 .ARM.attributes
                0x000006b0       0x34 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
 .ARM.attributes
                0x000006e4       0x34 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .ARM.attributes
                0x00000718       0x34 libGENERATED_SDK_TARGET.a(system_YTM32B1MD1.c.o)
 .ARM.attributes
                0x0000074c       0x34 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .ARM.attributes
                0x00000780       0x34 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .ARM.attributes
                0x000007b4       0x34 libGENERATED_SDK_TARGET.a(uds.c.o)
 .ARM.attributes
                0x000007e8       0x34 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
 .ARM.attributes
                0x0000081c       0x22 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubsf3.o)
 .ARM.attributes
                0x0000083e       0x22 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivsf3.o)
 .ARM.attributes
                0x00000860       0x22 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_fixunssfsi.o)
 .ARM.attributes
                0x00000882       0x22 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o)
 .ARM.attributes
                0x000008a4       0x32 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
 .ARM.attributes
                0x000008d6       0x22 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_dvmd_tls.o)
 .ARM.attributes
                0x000008f8       0x20 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-memcpy.o)
 .ARM.attributes
                0x00000918       0x32 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-rand.o)
 .ARM.attributes
                0x0000094a       0x32 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-impure.o)

.debug_frame    0x00000000     0x2c4c
 .debug_frame   0x00000000       0x68 CMakeFiles/uds_lin_bootloader.elf.dir/app/main.c.o
 .debug_frame   0x00000068       0x20 CMakeFiles/uds_lin_bootloader.elf.dir/app/GenerateKeyExImpl.c.o
 .debug_frame   0x00000088      0x22c CMakeFiles/uds_lin_bootloader.elf.dir/app/Uds_Service.c.o
 .debug_frame   0x000002b4       0xd0 CMakeFiles/uds_lin_bootloader.elf.dir/app/boot_jump.c.o
 .debug_frame   0x00000384       0x20 libGENERATED_CONFIG_TARGET.a(lin_lib_config.c.o)
 .debug_frame   0x000003a4      0x710 libGENERATED_CONFIG_TARGET.a(vector_table_copy.c.o)
 .debug_frame   0x00000ab4       0x34 libGENERATED_CONFIG_TARGET.a(RamInit1.c.o)
 .debug_frame   0x00000ae8       0x20 libGENERATED_CONFIG_TARGET.a(RamInit2.c.o)
 .debug_frame   0x00000b08       0x28 libGENERATED_CONFIG_TARGET.a(interrupt_config.c.o)
 .debug_frame   0x00000b30       0x20 libGENERATED_CONFIG_TARGET.a(uds_config.c.o)
 .debug_frame   0x00000b50      0x154 libGENERATED_SDK_TARGET.a(clock_YTM32B1Mx.c.o)
 .debug_frame   0x00000ca4      0x19c libGENERATED_SDK_TARGET.a(pins_driver.c.o)
 .debug_frame   0x00000e40       0x40 libGENERATED_SDK_TARGET.a(pins_port_hw_access.c.o)
 .debug_frame   0x00000e80       0xb0 libGENERATED_SDK_TARGET.a(interrupt_manager.c.o)
 .debug_frame   0x00000f30      0x37c libGENERATED_SDK_TARGET.a(dma_driver.c.o)
 .debug_frame   0x000012ac      0x124 libGENERATED_SDK_TARGET.a(dma_hw_access.c.o)
 .debug_frame   0x000013d0      0x1ac libGENERATED_SDK_TARGET.a(dma_irq.c.o)
 .debug_frame   0x0000157c      0x254 libGENERATED_SDK_TARGET.a(linflexd_lin_driver.c.o)
 .debug_frame   0x000017d0       0x58 libGENERATED_SDK_TARGET.a(linflexd_lin_irq.c.o)
 .debug_frame   0x00001828      0x16c libGENERATED_SDK_TARGET.a(ptmr_driver.c.o)
 .debug_frame   0x00001994      0x184 libGENERATED_SDK_TARGET.a(lptmr_driver.c.o)
 .debug_frame   0x00001b18       0x20 libGENERATED_SDK_TARGET.a(lptmr_hw_access.c.o)
 .debug_frame   0x00001b38      0x204 libGENERATED_SDK_TARGET.a(hcu_driver.c.o)
 .debug_frame   0x00001d3c       0x28 libGENERATED_SDK_TARGET.a(hcu_irq.c.o)
 .debug_frame   0x00001d64       0x90 libGENERATED_SDK_TARGET.a(trng_driver.c.o)
 .debug_frame   0x00001df4       0xf0 libGENERATED_SDK_TARGET.a(trng_hw_access.c.o)
 .debug_frame   0x00001ee4      0x368 libGENERATED_SDK_TARGET.a(flash_driver.c.o)
 .debug_frame   0x0000224c       0x48 libGENERATED_SDK_TARGET.a(system_YTM32B1MD1.c.o)
 .debug_frame   0x00002294      0x314 libGENERATED_SDK_TARGET.a(lin_core.c.o)
 .debug_frame   0x000025a8      0x310 libGENERATED_SDK_TARGET.a(lin_tp.c.o)
 .debug_frame   0x000028b8      0x154 libGENERATED_SDK_TARGET.a(uds.c.o)
 .debug_frame   0x00002a0c       0xf8 libGENERATED_SDK_TARGET.a(osif_baremetal.c.o)
 .debug_frame   0x00002b04       0x4c c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubsf3.o)
 .debug_frame   0x00002b50       0x38 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivsf3.o)
 .debug_frame   0x00002b88       0x24 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_arm_fixunssfsi.o)
 .debug_frame   0x00002bac       0x2c c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o)
 .debug_frame   0x00002bd8       0x38 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
 .debug_frame   0x00002c10       0x3c c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\libc.a(lib_a-rand.o)
