/*
 * Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 * All rights reserved.
 *
 * YUNTU Confidential. This software is owned or controlled by YUNTU and may
 * only be used strictly in accordance with the applicable license terms. By expressly
 * accepting such terms or by downloading, installing, activating and/or otherwise
 * using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms. If you do not agree to be
 * bound by the applicable license terms, then you may not retain, install,
 * activate or otherwise use the software. The production use license in
 * Section 2.3 is expressly granted for this software.
 */

#ifndef UDS_H_
#define UDS_H_

/*******************************************************************************
 * Includes
 ******************************************************************************/
#include <stdint.h>
#include <stdbool.h>
#include "uds_types.h"
#include "uds_config.h"

/**
 * @brief This function initialize the uds state.
 * 
 */
void Uds_Init(void);

/**
 * @brief This function send the positive response.
 * 
 * @param[in] channel Uds channel
 * @param[in] sid Service id
 * @param[in] data Data to send
 * @param[in] dataLen Data length
 * @return uds_bool_t 
 */
uds_bool_t Uds_SendPositiveResponse(uds_channel_t channel, const uds_sid_t sid, const uds_u8_t *data, const uds_u32_t dataLen);

/**
 * @brief This function send the negative response.
 * 
 * @param[in] channel Uds channel
 * @param[in] sid Service id
 * @param[in] nrc Negative response code
 * @param[in] data Data to send
 * @param[in] dataLen Data length
 * @return uds_bool_t 
 */
uds_bool_t Uds_SendNegativeResponse(uds_channel_t channel, const uds_sid_t sid, uds_nrc_t nrc, const uds_u8_t *data, const uds_u32_t dataLen);

/**
 * @brief This function called when no response need to send.
 * 
 * @param[in] channel Uds channel
 * @return uds_bool_t 
 */
uds_bool_t Uds_NoResponse(uds_channel_t channel);

/**
 * @brief Main function of uds.
 * 
 */
void Uds_MainFunction(void);

/**
 * @brief This function is used to service s3 timer.
 * 
 * @param[in] expireMillisecond Called period
 */
void Uds_TimeService(uds_u32_t expireMillisecond);

/**
 * @brief This function is used to set the param of service callback function.
 * 
 * @param[in] channel Uds channel
 * @param[in] param Param
 * @return uds_bool_t 
 */
uds_bool_t Uds_SetupTmpParam(uds_channel_t channel, void *param);


/**
 * @brief This function is used to set the active session of the channel.
 * 
 * @param[in] channel Uds channel
 * @param[in] sessionMode Session mode
 */
void Uds_SetSession(uds_channel_t channel, const uds_u8_t sessionMode);

/**
 * @brief This function is used to get the active session of the channel.
 * 
 * @param[in] channel Uds channel
 * @return uds_u8_t 
 */
uds_u8_t Uds_GetSession(uds_channel_t channel);

/**
 * @brief This function is used to set the secure level of the channel.
 * 
 * @param[in] channel Uds channel
 * @param[in] secureLevel Secure level
 */
void Uds_SetSecureLevel(uds_channel_t channel, const uds_u32_t secureLevel);

/**
 * @brief This function is used to get the secure level of the channel.
 * 
 * @param[in] channel Uds channel
 * @return uds_u32_t 
 */
uds_u32_t Uds_GetSecureLevel(uds_channel_t channel);


#ifdef UDS_ENABLE_CAN
/**
 * @brief This function is the transmit callback of can_stack.
 * 
 * @param[in] txSduId The can_stack channel id.
 * @param[in] res Result of transmit.
 */
void Uds_CanTpTxConfirm(pdu_id_type txSduId, cantp_core_res_type res);

/**
 * @brief This function is the receive callback of can_stack.
 * 
 * @param[in] txSduId The can_stack channel id.
 * @param[in] res Result of receive.
 */
void Uds_CanTpRxIndicate(pdu_id_type txSduId, cantp_core_res_type res);
#endif

#endif /* UDS_H_ */
