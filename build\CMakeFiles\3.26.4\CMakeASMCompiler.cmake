set(CMAKE_ASM_COMPILER "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc.exe")
set(CMAKE_ASM_COMPILER_ARG1 "")
set(CMAKE_AR "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc-ar.exe")
set(CMAKE_ASM_COMPILER_AR "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc-ar.exe")
set(CMAKE_RANLIB "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-ranlib.exe")
set(CMAKE_ASM_COMPILER_RANLIB "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc-ranlib.exe")
set(CMAKE_LINKER "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++.exe")
set(CMAKE_MT "")
set(CMAKE_ASM_COMPILER_LOADED 1)
set(CMAKE_ASM_COMPILER_ID "GNU")
set(CMAKE_ASM_COMPILER_VERSION "")
set(CMAKE_ASM_COMPILER_ENV_VAR "ASM")




set(CMAKE_ASM_IGNORE_EXTENSIONS h;H;o;O;obj;OBJ;def;DEF;rc;RC)
set(CMAKE_ASM_LINKER_PREFERENCE 0)


