/*
 * Copyright 2020-2022 Yuntu Microelectronics co.,ltd
 * All rights reserved.
 *
 * YUNTU Confidential. This software is owned or controlled by YUNTU and may
 * only be used strictly in accordance with the applicable license terms. By expressly
 * accepting such terms or by downloading, installing, activating and/or otherwise
 * using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms. If you do not agree to be
 * bound by the applicable license terms, then you may not retain, install,
 * activate or otherwise use the software. The production use license in
 * Section 2.3 is expressly granted for this software.
 */

/*!
 * @file linflexd_lin_irq.h
 * @note CODE GENERATED BY TOOL AUTOMATICALLY, DO NOT MODIFY MANUALLY.
 */

#include "linflexd_hw_access.h"

/*!
 * @brief Or'ed interrupt handler, implemented in driver c file.
 */
void LINFlexD_LIN_DRV_IRQHandler(uint32_t instance);

/*!
 * @brief Rx interrupt handler, implemented in driver c file.
 */
void LINFlexD_LIN_DRV_RxIRQHandler(uint32_t instance);

/*!
 * @brief Tx interrupt handler, implemented in driver c file.
 */
void LINFlexD_LIN_DRV_TxIRQHandler(uint32_t instance);

/*!
 * @brief Error interrupt handler, implemented in driver c file.
 */
void LINFlexD_LIN_DRV_ErrIRQHandler(uint32_t instance);

/*!
 * @brief ISR declarations - implemented in linflexd_lin_irq.c.
 */
#if (FEATURE_LINFlexD_ORED_INT_LINES)

    /* ISR declarations for rx/tx/err interrupts or'ed together */
    #if (LINFlexD_INSTANCE_COUNT > 0U)
    void LINFlexD0_LIN_IRQHandler(void);
    #endif
    #if (LINFlexD_INSTANCE_COUNT > 1U)
    void LINFlexD1_LIN_IRQHandler(void);
    #endif
    #if (LINFlexD_INSTANCE_COUNT > 2U)
    void LINFlexD2_LIN_IRQHandler(void);
    #endif
    #if (LINFlexD_INSTANCE_COUNT > 3U)
    void LINFlexD3_LIN_IRQHandler(void);
    #endif
    #if (LINFlexD_INSTANCE_COUNT > 4U)
    void LINFlexD4_LIN_IRQHandler(void);
    #endif
    #if (LINFlexD_INSTANCE_COUNT > 5U)
    void LINFlexD5_LIN_IRQHandler(void);
    #endif

    /* ISR array for LINFlexD LIN driver */
    extern const isr_t g_LinLINFlexDIsr[LINFlexD_INSTANCE_COUNT];

#elif (FEATURE_LINFlexD_RX_TX_ERR_INT_LINES)

    /* ISR declarations for separate rx/tx/err interrupts */
    #if (LINFlexD_INSTANCE_COUNT > 0U)
    void LINFlexD0_LIN_RxIRQHandler(void);
    void LINFlexD0_LIN_TxIRQHandler(void);
    void LINFlexD0_LIN_ErrIRQHandler(void);
    #endif

    #if (LINFlexD_INSTANCE_COUNT > 1U)
    void LINFlexD1_LIN_RxIRQHandler(void);
    void LINFlexD1_LIN_TxIRQHandler(void);
    void LINFlexD1_LIN_ErrIRQHandler(void);
    #endif

    #if (LINFlexD_INSTANCE_COUNT > 2U)
    void LINFlexD2_LIN_RxIRQHandler(void);
    void LINFlexD2_LIN_TxIRQHandler(void);
    void LINFlexD2_LIN_ErrIRQHandler(void);
    #endif

    #if (LINFlexD_INSTANCE_COUNT > 3U)
    void LINFlexD3_LIN_RxIRQHandler(void);
    void LINFlexD3_LIN_TxIRQHandler(void);
    void LINFlexD3_LIN_ErrIRQHandler(void);
    #endif

    #if (LINFlexD_INSTANCE_COUNT > 4U)
    void LINFlexD4_LIN_RxIRQHandler(void);
    void LINFlexD4_LIN_TxIRQHandler(void);
    void LINFlexD4_LIN_ErrIRQHandler(void);
    #endif

    #if (LINFlexD_INSTANCE_COUNT > 5U)
    void LINFlexD5_LIN_RxIRQHandler(void);
    void LINFlexD5_LIN_TxIRQHandler(void);
    void LINFlexD5_LIN_ErrIRQHandler(void);
    #endif


    /* Rx ISR array for LINFlexD LIN driver */
    extern const isr_t g_LinLINFlexDRxIsr[LINFlexD_INSTANCE_COUNT];

    /* Tx ISR array for LINFlexD LIN driver */
    extern const isr_t g_LinLINFlexDTxIsr[LINFlexD_INSTANCE_COUNT];

    /* Err ISR array for LINFlexD LIN driver */
    extern const isr_t g_LinLINFlexDErrIsr[LINFlexD_INSTANCE_COUNT];

#endif
