{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/bin/cmake.exe", "cpack": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/bin/cpack.exe", "ctest": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/bin/ctest.exe", "root": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26"}, "version": {"isDirty": false, "major": 3, "minor": 26, "patch": 4, "string": "3.26.4", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-872c7c71c2704cbe0418.json", "kind": "codemodel", "version": {"major": 2, "minor": 5}}, {"jsonFile": "cache-v2-9f8f92ccb1b6689ecf4f.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-e1811a80e57f9f0c78cb.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-1a3aa6593c5eabf8b19f.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-9f8f92ccb1b6689ecf4f.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-872c7c71c2704cbe0418.json", "kind": "codemodel", "version": {"major": 2, "minor": 5}}, {"jsonFile": "toolchains-v1-1a3aa6593c5eabf8b19f.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-e1811a80e57f9f0c78cb.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}]}}}}