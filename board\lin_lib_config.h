/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file lin_lib_config.h
 * @brief 
 * 
 */




#ifndef LIN_LIB_CONFIG_H_
#define LIN_LIB_CONFIG_H_

#include "lin_types.h"

#include "linflexd_lin_driver.h"

#define LIN_IFC_INST 1





/*  === start of LinConfig_0 === */

#define LIN_IFC_LinConfig_0 ((l_ifc_handle)0)

/* signal handle and flag macro define */
/* Motor1_Dynamic_Sig*/
#define LinConfig_0_Motor1_Dynamic_Sig_signal_handle (l_signal_handle)(0<<16|0)
#define LinConfig_0_Motor1_Dynamic_Sig_flag_handle (l_flag_handle)(0<<24|0)
#define l_u8_rd_LinConfig_0_Motor1_Dynamic_Sig() l_u8_rd(LinConfig_0_Motor1_Dynamic_Sig_signal_handle)
#define l_u8_wr_LinConfig_0_Motor1_Dynamic_Sig(v) l_u8_wr(LinConfig_0_Motor1_Dynamic_Sig_signal_handle,v)
#define l_flg_clr_LinConfig_0_Motor1_Dynamic_Sig() l_flg_clr(LinConfig_0_Motor1_Dynamic_Sig_flag_handle)
#define l_flg_tst_LinConfig_0_Motor1_Dynamic_Sig() l_flg_tst(LinConfig_0_Motor1_Dynamic_Sig_flag_handle) 
/* Motor1ErrorCode*/
#define LinConfig_0_Motor1ErrorCode_signal_handle (l_signal_handle)(0<<16|1)
#define LinConfig_0_Motor1ErrorCode_flag_handle (l_flag_handle)(0<<24|1)
#define l_u8_rd_LinConfig_0_Motor1ErrorCode() l_u8_rd(LinConfig_0_Motor1ErrorCode_signal_handle)
#define l_u8_wr_LinConfig_0_Motor1ErrorCode(v) l_u8_wr(LinConfig_0_Motor1ErrorCode_signal_handle,v)
#define l_flg_clr_LinConfig_0_Motor1ErrorCode() l_flg_clr(LinConfig_0_Motor1ErrorCode_flag_handle)
#define l_flg_tst_LinConfig_0_Motor1ErrorCode() l_flg_tst(LinConfig_0_Motor1ErrorCode_flag_handle) 
/* Motor1ErrorValue*/
#define LinConfig_0_Motor1ErrorValue_signal_handle (l_signal_handle)(0<<16|2)
#define LinConfig_0_Motor1ErrorValue_flag_handle (l_flag_handle)(0<<24|2)
#define l_u8_rd_LinConfig_0_Motor1ErrorValue() l_u8_rd(LinConfig_0_Motor1ErrorValue_signal_handle)
#define l_u8_wr_LinConfig_0_Motor1ErrorValue(v) l_u8_wr(LinConfig_0_Motor1ErrorValue_signal_handle,v)
#define l_flg_clr_LinConfig_0_Motor1ErrorValue() l_flg_clr(LinConfig_0_Motor1ErrorValue_flag_handle)
#define l_flg_tst_LinConfig_0_Motor1ErrorValue() l_flg_tst(LinConfig_0_Motor1ErrorValue_flag_handle) 
/* Motor1LinError*/
#define LinConfig_0_Motor1LinError_signal_handle (l_signal_handle)(0<<16|3)
#define LinConfig_0_Motor1LinError_flag_handle (l_flag_handle)(0<<24|3)
#define l_bool_rd_LinConfig_0_Motor1LinError() l_bool_rd(LinConfig_0_Motor1LinError_signal_handle)
#define l_bool_wr_LinConfig_0_Motor1LinError(v) l_bool_wr(LinConfig_0_Motor1LinError_signal_handle,v)
#define l_flg_clr_LinConfig_0_Motor1LinError() l_flg_clr(LinConfig_0_Motor1LinError_flag_handle)
#define l_flg_tst_LinConfig_0_Motor1LinError() l_flg_tst(LinConfig_0_Motor1LinError_flag_handle) 
/* Motor1Position*/
#define LinConfig_0_Motor1Position_signal_handle (l_signal_handle)(0<<16|4)
#define LinConfig_0_Motor1Position_flag_handle (l_flag_handle)(0<<24|4)
#define l_bytes_rd_LinConfig_0_Motor1Position(start,count,data) l_bytes_rd(LinConfig_0_Motor1Position_signal_handle,start,count,data)
#define l_bytes_wr_LinConfig_0_Motor1Position(start,count,data) l_bytes_wr(LinConfig_0_Motor1Position_signal_handle,start,count,data)
#define l_flg_clr_LinConfig_0_Motor1Position() l_flg_clr(LinConfig_0_Motor1Position_flag_handle)
#define l_flg_tst_LinConfig_0_Motor1Position() l_flg_tst(LinConfig_0_Motor1Position_flag_handle) 
/* Motor1Temp*/
#define LinConfig_0_Motor1Temp_signal_handle (l_signal_handle)(0<<16|5)
#define LinConfig_0_Motor1Temp_flag_handle (l_flag_handle)(0<<24|5)
#define l_u8_rd_LinConfig_0_Motor1Temp() l_u8_rd(LinConfig_0_Motor1Temp_signal_handle)
#define l_u8_wr_LinConfig_0_Motor1Temp(v) l_u8_wr(LinConfig_0_Motor1Temp_signal_handle,v)
#define l_flg_clr_LinConfig_0_Motor1Temp() l_flg_clr(LinConfig_0_Motor1Temp_flag_handle)
#define l_flg_tst_LinConfig_0_Motor1Temp() l_flg_tst(LinConfig_0_Motor1Temp_flag_handle) 
/* MotorDirection*/
#define LinConfig_0_MotorDirection_signal_handle (l_signal_handle)(0<<16|6)
#define LinConfig_0_MotorDirection_flag_handle (l_flag_handle)(0<<24|6)
#define l_u8_rd_LinConfig_0_MotorDirection() l_u8_rd(LinConfig_0_MotorDirection_signal_handle)
#define l_flg_clr_LinConfig_0_MotorDirection() l_flg_clr(LinConfig_0_MotorDirection_flag_handle)
#define l_flg_tst_LinConfig_0_MotorDirection() l_flg_tst(LinConfig_0_MotorDirection_flag_handle) 
/* MotorSelection*/
#define LinConfig_0_MotorSelection_signal_handle (l_signal_handle)(0<<16|7)
#define LinConfig_0_MotorSelection_flag_handle (l_flag_handle)(0<<24|7)
#define l_u8_rd_LinConfig_0_MotorSelection() l_u8_rd(LinConfig_0_MotorSelection_signal_handle)
#define l_flg_clr_LinConfig_0_MotorSelection() l_flg_clr(LinConfig_0_MotorSelection_flag_handle)
#define l_flg_tst_LinConfig_0_MotorSelection() l_flg_tst(LinConfig_0_MotorSelection_flag_handle) 
/* MotorSpeed*/
#define LinConfig_0_MotorSpeed_signal_handle (l_signal_handle)(0<<16|8)
#define LinConfig_0_MotorSpeed_flag_handle (l_flag_handle)(0<<24|8)
#define l_u16_rd_LinConfig_0_MotorSpeed() l_u16_rd(LinConfig_0_MotorSpeed_signal_handle)
#define l_flg_clr_LinConfig_0_MotorSpeed() l_flg_clr(LinConfig_0_MotorSpeed_flag_handle)
#define l_flg_tst_LinConfig_0_MotorSpeed() l_flg_tst(LinConfig_0_MotorSpeed_flag_handle) 

/* frame flag macro define */
#define LinConfig_0_Motor1_Dynamic_flag_handle (l_flag_handle)(0<<24|1<<16|0)
#define l_flg_clr_LinConfig_0_Motor1_Dynamic() l_flg_clr(LinConfig_0_Motor1_Dynamic_flag_handle)
#define l_flg_tst_LinConfig_0_Motor1_Dynamic() l_flg_tst(LinConfig_0_Motor1_Dynamic_flag_handle) 
#define LinConfig_0_Motor1State_Cycl_flag_handle (l_flag_handle)(0<<24|1<<16|1)
#define l_flg_clr_LinConfig_0_Motor1State_Cycl() l_flg_clr(LinConfig_0_Motor1State_Cycl_flag_handle)
#define l_flg_tst_LinConfig_0_Motor1State_Cycl() l_flg_tst(LinConfig_0_Motor1State_Cycl_flag_handle) 
#define LinConfig_0_Motor1State_Event_flag_handle (l_flag_handle)(0<<24|1<<16|2)
#define l_flg_clr_LinConfig_0_Motor1State_Event() l_flg_clr(LinConfig_0_Motor1State_Event_flag_handle)
#define l_flg_tst_LinConfig_0_Motor1State_Event() l_flg_tst(LinConfig_0_Motor1State_Event_flag_handle) 
#define LinConfig_0_MotorControl_flag_handle (l_flag_handle)(0<<24|1<<16|3)
#define l_flg_clr_LinConfig_0_MotorControl() l_flg_clr(LinConfig_0_MotorControl_flag_handle)
#define l_flg_tst_LinConfig_0_MotorControl() l_flg_tst(LinConfig_0_MotorControl_flag_handle) 
#define LinConfig_0_ETF_MotorStates_flag_handle (l_flag_handle)(0<<24|1<<16|4)
#define l_flg_clr_LinConfig_0_ETF_MotorStates() l_flg_clr(LinConfig_0_ETF_MotorStates_flag_handle)
#define l_flg_tst_LinConfig_0_ETF_MotorStates() l_flg_tst(LinConfig_0_ETF_MotorStates_flag_handle) 
#define LinConfig_0_master_req_flag_handle (l_flag_handle)(0<<24|1<<16|5)
 #define LinConfig_0_slave_resp_flag_handle (l_flag_handle)(0<<24|1<<16|6)
/*  === end of LinConfig_0 === */











#define LIN_HAS_SLAVE



typedef struct
{
    l_u16 ifcStatus;                /*!< interface status for l_ifc_read_status*/
    lin_frame_status_t frameStatus; /*!< frame status*/
	lin_error_t error;              /*!< last error code */
	lin_frame_sm_t frameSm;         /*!< frame state machine */
	const lin_frame_t* activeFrame; /*!< active frame */
    int frameTimeout;               /*!< timeout count */
    int idleTimeout;                /*!< idle timeout count */

#ifdef LIN_USE_UART
    uint8_t dataBuffer[8];
    uint8_t dataLength;
    uint8_t id;
    lin_event_id_t event;           /*!< lin event id */
#else
	linflexd_event_id_t event;     /*!< linflexd event id */
	linflexd_frame_t frameBuf;     /*!< frame buffer */
#endif
    
    l_u8* dataPool;                 /*!< data pool */
    l_u8* frameFlagPool;            /*!< frame flag pool */
    l_u8* signalFlagPool;           /*!< signal flag pool */
    l_u8* updateFlagPool;           /*!< update flag pool */
    
    lin_master_state_t* masterState;    /*!< master state */
    lin_slave_state_t* slaveState;      /*!< slave state */
    lin_tp_state_t* tpState;            /*!< transport layer state */
    l_bool tpInit;                  /*!< transport layer init */

#ifdef LIN_DEBUG
    l_u16 preSendCnt;
    l_u16 sendCnt;
#endif
}lin_core_state_t;


extern const lin_config_t* g_linGlobalConfig[LIN_IFC_INST];
extern lin_core_state_t g_linGlobalState[LIN_IFC_INST];

extern void lin_config_init();
#endif