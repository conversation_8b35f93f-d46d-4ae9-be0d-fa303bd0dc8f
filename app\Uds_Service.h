
#include "uds_ip.h"


#define  DCM_C_T_P2SERVER_MAX			50		//ms	P2server_Max
#define  DCM_C_T_P2_SERVER_MAX			5000	//ms	P2*server_Max
#define  DCM_C_T_FBL_POLLING			20//200		//ms
#define  DCM_C_T_FBL_STAYINBOOT			500//200		//ms

#define  ATTEMPT_CNT_MAX                5

extern uds_ip_api_t uds_global_ip_api;

extern void UDS_IP_SessionA(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
extern void UDS_IP_TesterPresentA(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
extern void UDS_IP_RoutineControlEraseFlashMemoryA(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
extern void UDS_IP_RequestDownloadA(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
extern void UDS_IP_TransferDataA(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
extern void UDS_IP_SecurityAccessA(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
extern void UDS_IP_RoutineControlCrcCheckA(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
extern void UDS_IP_ECUResetSoftResetA(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
extern void UDS_IP_RequestTransferExitA(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);


