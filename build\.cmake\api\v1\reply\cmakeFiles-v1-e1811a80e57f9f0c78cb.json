{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/3.26.4/CMakeSystem.cmake"}, {"path": "cmake/gcc.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.26.4/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.26.4/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Compiler/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Platform/Linux-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Platform/Linux-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeCommonLanguageInclude.cmake"}, {"path": "cmake/config.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakePrintHelpers.cmake"}, {"path": "cmake/configLib.cmake"}, {"path": "cmake/Toolchain/Tools.cmake"}, {"path": "cmake/Toolchain/GCC.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.26.4/CMakeASMCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeASMInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Compiler/GNU-ASM.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/Compiler/GNU.cmake"}, {"path": "cmake/configCore.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakePrintHelpers.cmake"}, {"path": "cmake/targets/GENERATED_CONFIG_TARGET.cmake"}, {"path": "cmake/targets/GENERATED_SDK_TARGET.cmake"}], "kind": "cmakeFiles", "paths": {"build": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "source": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader"}, "version": {"major": 1, "minor": 0}}