{"archive": {}, "artifacts": [{"path": "libGENERATED_CONFIG_TARGET.a"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_compile_options", "compilerSpecificCompileOptions", "configcore", "target_compile_definitions", "target_include_directories", "target_link_libraries"], "files": ["cmake/targets/GENERATED_CONFIG_TARGET.cmake", "CMakeLists.txt", "cmake/Toolchain/GCC.cmake", "cmake/configCore.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 17, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 31, "parent": 2}, {"command": 4, "file": 0, "line": 37, "parent": 2}, {"command": 3, "file": 3, "line": 284, "parent": 4}, {"command": 2, "file": 2, "line": 16, "parent": 5}, {"command": 2, "file": 2, "line": 20, "parent": 5}, {"command": 2, "file": 2, "line": 30, "parent": 5}, {"command": 2, "file": 2, "line": 33, "parent": 5}, {"command": 2, "file": 0, "line": 43, "parent": 2}, {"command": 5, "file": 3, "line": 188, "parent": 4}, {"command": 5, "file": 3, "line": 180, "parent": 4}, {"command": 5, "file": 0, "line": 39, "parent": 2}, {"command": 6, "file": 0, "line": 33, "parent": 2}, {"command": 7, "file": 0, "line": 49, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-mcpu=cortex-m33 -g"}, {"backtrace": 6, "fragment": "-mlittle-endian"}, {"backtrace": 7, "fragment": "-mthumb"}, {"backtrace": 8, "fragment": "-specs=nosys.specs"}, {"backtrace": 9, "fragment": "-Wall"}, {"backtrace": 9, "fragment": "-Werror=all"}, {"backtrace": 9, "fragment": "-Wno-error=comment"}, {"backtrace": 9, "fragment": "-g"}, {"backtrace": 9, "fragment": "-O1"}, {"backtrace": 9, "fragment": "-ffunction-sections"}, {"backtrace": 9, "fragment": "-fdata-sections"}, {"backtrace": 9, "fragment": "-Wno-error=unused-variable"}, {"backtrace": 9, "fragment": "-Wno-error=unused-parameter"}, {"backtrace": 9, "fragment": "-Wno-error=unused-function"}, {"backtrace": 9, "fragment": "-Wno-error=unused-but-set-variable"}, {"backtrace": 9, "fragment": "-Wno-error=unused-result"}, {"backtrace": 9, "fragment": "-Wno-error=maybe-uninitialized"}, {"backtrace": 9, "fragment": "-Wno-error=sign-compare"}, {"backtrace": 9, "fragment": "-Wno-error=strict-aliasing"}, {"backtrace": 9, "fragment": "-Wno-error=unknown-pragmas"}, {"backtrace": 9, "fragment": "-Wno-error=format"}, {"backtrace": 10, "fragment": "-fdiagnostics-color=always"}], "defines": [{"backtrace": 11, "define": "ARMCM33_DSP_FP"}, {"backtrace": 12, "define": "CORTEXM"}, {"backtrace": 13, "define": "CPU_YTM32B1MD1"}, {"backtrace": 13, "define": "YTM32B1MD1"}], "includes": [{"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif"}], "language": "C", "sourceIndexes": [0, 1, 2, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}, {"compileCommandFragments": [{"fragment": "-mcpu=cortex-m33 -g"}, {"backtrace": 6, "fragment": "-mlittle-endian"}, {"backtrace": 7, "fragment": "-mthumb"}, {"backtrace": 8, "fragment": "-specs=nosys.specs"}, {"backtrace": 9, "fragment": "-Wall"}, {"backtrace": 9, "fragment": "-Werror=all"}, {"backtrace": 9, "fragment": "-Wno-error=comment"}, {"backtrace": 9, "fragment": "-g"}, {"backtrace": 9, "fragment": "-O1"}, {"backtrace": 9, "fragment": "-ffunction-sections"}, {"backtrace": 9, "fragment": "-fdata-sections"}, {"backtrace": 9, "fragment": "-Wno-error=unused-variable"}, {"backtrace": 9, "fragment": "-Wno-error=unused-parameter"}, {"backtrace": 9, "fragment": "-Wno-error=unused-function"}, {"backtrace": 9, "fragment": "-Wno-error=unused-but-set-variable"}, {"backtrace": 9, "fragment": "-Wno-error=unused-result"}, {"backtrace": 9, "fragment": "-Wno-error=maybe-uninitialized"}, {"backtrace": 9, "fragment": "-Wno-error=sign-compare"}, {"backtrace": 9, "fragment": "-Wno-error=strict-aliasing"}, {"backtrace": 9, "fragment": "-Wno-error=unknown-pragmas"}, {"backtrace": 9, "fragment": "-Wno-error=format"}, {"backtrace": 10, "fragment": "-fdiagnostics-color=always"}], "defines": [{"backtrace": 11, "define": "ARMCM33_DSP_FP"}, {"backtrace": 12, "define": "CORTEXM"}, {"backtrace": 13, "define": "CPU_YTM32B1MD1"}, {"backtrace": 13, "define": "YTM32B1MD1"}], "includes": [{"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif"}], "language": "ASM", "sourceIndexes": [3, 4, 6]}], "dependencies": [{"id": "GENERATED_SDK_TARGET::@6890427a1f51a3e7e1df"}], "id": "GENERATED_CONFIG_TARGET::@6890427a1f51a3e7e1df", "name": "GENERATED_CONFIG_TARGET", "nameOnDisk": "libGENERATED_CONFIG_TARGET.a", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}, {"name": "", "sourceIndexes": [3, 4, 6]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "board/clock_config.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "board/pin_mux.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "board/lin_lib_config.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 1, "path": "board/startup.S", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 1, "path": "board/vector.S", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "board/vector_table_copy.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 1, "path": "board/RamInit0.S", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "board/RamInit1.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "board/RamInit2.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "board/interrupt_config.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "board/dma_config.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "board/linflexd_lin_config.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "board/ptmr_config.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "board/lptmr_config.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "board/crc_config.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "board/hcu_config.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "board/flash_config.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "board/uds_config.c", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}