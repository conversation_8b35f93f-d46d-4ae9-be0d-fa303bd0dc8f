{"archive": {}, "artifacts": [{"path": "libGENERATED_SDK_TARGET.a"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_compile_options", "compilerSpecificCompileOptions", "configcore", "target_compile_definitions", "target_include_directories", "target_link_libraries"], "files": ["cmake/targets/GENERATED_SDK_TARGET.cmake", "CMakeLists.txt", "cmake/Toolchain/GCC.cmake", "cmake/configCore.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 18, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 56, "parent": 2}, {"command": 4, "file": 0, "line": 62, "parent": 2}, {"command": 3, "file": 3, "line": 284, "parent": 4}, {"command": 2, "file": 2, "line": 16, "parent": 5}, {"command": 2, "file": 2, "line": 20, "parent": 5}, {"command": 2, "file": 2, "line": 30, "parent": 5}, {"command": 2, "file": 2, "line": 33, "parent": 5}, {"command": 2, "file": 0, "line": 68, "parent": 2}, {"command": 5, "file": 3, "line": 188, "parent": 4}, {"command": 5, "file": 3, "line": 180, "parent": 4}, {"command": 5, "file": 0, "line": 64, "parent": 2}, {"command": 6, "file": 0, "line": 58, "parent": 2}, {"command": 7, "file": 0, "line": 74, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-mcpu=cortex-m33 -g"}, {"backtrace": 6, "fragment": "-mlittle-endian"}, {"backtrace": 7, "fragment": "-mthumb"}, {"backtrace": 8, "fragment": "-specs=nosys.specs"}, {"backtrace": 9, "fragment": "-Wall"}, {"backtrace": 9, "fragment": "-Werror=all"}, {"backtrace": 9, "fragment": "-Wno-error=comment"}, {"backtrace": 9, "fragment": "-g"}, {"backtrace": 9, "fragment": "-O1"}, {"backtrace": 9, "fragment": "-ffunction-sections"}, {"backtrace": 9, "fragment": "-fdata-sections"}, {"backtrace": 9, "fragment": "-Wno-error=unused-variable"}, {"backtrace": 9, "fragment": "-Wno-error=unused-parameter"}, {"backtrace": 9, "fragment": "-Wno-error=unused-function"}, {"backtrace": 9, "fragment": "-Wno-error=unused-but-set-variable"}, {"backtrace": 9, "fragment": "-Wno-error=unused-result"}, {"backtrace": 9, "fragment": "-Wno-error=maybe-uninitialized"}, {"backtrace": 9, "fragment": "-Wno-error=sign-compare"}, {"backtrace": 9, "fragment": "-Wno-error=strict-aliasing"}, {"backtrace": 9, "fragment": "-Wno-error=unknown-pragmas"}, {"backtrace": 9, "fragment": "-Wno-error=format"}, {"backtrace": 10, "fragment": "-fdiagnostics-color=always"}], "defines": [{"backtrace": 11, "define": "ARMCM33_DSP_FP"}, {"backtrace": 12, "define": "CORTEXM"}, {"backtrace": 13, "define": "CPU_YTM32B1MD1"}, {"backtrace": 13, "define": "YTM32B1MD1"}], "includes": [{"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx"}, {"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins"}, {"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma"}, {"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd"}, {"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr"}, {"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr"}, {"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc"}, {"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu"}, {"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng"}, {"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash"}, {"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc"}, {"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common"}, {"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices"}, {"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include"}, {"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup"}, {"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc"}, {"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc"}, {"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include"}, {"backtrace": 14, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif"}, {"backtrace": 15, "path": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board"}], "language": "C", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24]}], "id": "GENERATED_SDK_TARGET::@6890427a1f51a3e7e1df", "name": "GENERATED_SDK_TARGET", "nameOnDisk": "libGENERATED_SDK_TARGET.a", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "platform/drivers/src/clock/YTM32B1Mx/clock_YTM32B1Mx.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "platform/drivers/src/pins/pins_driver.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "platform/drivers/src/pins/pins_port_hw_access.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "platform/drivers/src/interrupt/interrupt_manager.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "platform/drivers/src/dma/dma_driver.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "platform/drivers/src/dma/dma_hw_access.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "platform/drivers/src/dma/dma_irq.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "platform/drivers/src/linflexd/linflexd_lin_driver.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "platform/drivers/src/linflexd/linflexd_lin_irq.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "platform/drivers/src/ptmr/ptmr_driver.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "platform/drivers/src/lptmr/lptmr_driver.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "platform/drivers/src/lptmr/lptmr_hw_access.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "platform/drivers/src/crc/crc_driver.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "platform/drivers/src/crc/crc_hw_access.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "platform/drivers/src/hcu/hcu_driver.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "platform/drivers/src/hcu/hcu_irq.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "platform/drivers/src/trng/trng_driver.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "platform/drivers/src/trng/trng_hw_access.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "platform/drivers/src/flash/flash_driver.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "platform/devices/YTM32B1MD1/startup/system_YTM32B1MD1.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "middleware/lin_stack/src/lin_core.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "middleware/lin_stack/src/lin_tp.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "middleware/uds/src/uds_ip.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "middleware/uds/src/uds.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "rtos/osif/osif_baremetal.c", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}