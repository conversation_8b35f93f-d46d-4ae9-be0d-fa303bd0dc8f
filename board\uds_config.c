#include "uds_types.h"
#include "uds_config.h"

#ifdef UDS_ENABLE_LIN
#include "lin_lib_config.h"
#endif

#ifdef UDS_ENABLE_CAN
#include "cantp_cfg.h"
#endif

#include "stddef.h"



static const uds_u8_t Inst0_rule_0_rxData[] ={ 0x10, };
static const uds_u8_t Inst0_rule_0_rxMask[] ={ 0xFF, };
static const uds_u8_t Inst0_rule_1_rxData[] ={ 0x3E, };
static const uds_u8_t Inst0_rule_1_rxMask[] ={ 0xFF, };
static const uds_u8_t Inst0_rule_2_rxData[] ={ 0x31,0x01,0xFF,0x00, };
static const uds_u8_t Inst0_rule_2_rxMask[] ={ 0xFF,0xFF,0xFF,0xFF, };
static const uds_u8_t Inst0_rule_3_rxData[] ={ 0x34, };
static const uds_u8_t Inst0_rule_3_rxMask[] ={ 0xFF, };
static const uds_u8_t Inst0_rule_4_rxData[] ={ 0x36, };
static const uds_u8_t Inst0_rule_4_rxMask[] ={ 0xFF, };
static const uds_u8_t Inst0_rule_5_rxData[] ={ 0x27, };
static const uds_u8_t Inst0_rule_5_rxMask[] ={ 0xFF, };
static const uds_u8_t Inst0_rule_6_rxData[] ={ 0x31,0x01,0x02,0x02, };
static const uds_u8_t Inst0_rule_6_rxMask[] ={ 0xFF,0xFF,0xFF,0xFF, };
static const uds_u8_t Inst0_rule_7_rxData[] ={ 0x11, };
static const uds_u8_t Inst0_rule_7_rxMask[] ={ 0xFF, };
static const uds_u8_t Inst0_rule_8_rxData[] ={ 0x37, };
static const uds_u8_t Inst0_rule_8_rxMask[] ={ 0xFF, };


extern void UDS_IP_SessionA(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
extern void UDS_IP_TesterPresentA(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
extern void UDS_IP_RoutineControlEraseFlashMemoryA(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
extern void UDS_IP_RequestDownloadA(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
extern void UDS_IP_TransferDataA(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
extern void UDS_IP_SecurityAccessA(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
extern void UDS_IP_RoutineControlCrcCheckA(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
extern void UDS_IP_ECUResetSoftResetA(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
extern void UDS_IP_RequestTransferExitA(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength, void* param);
static const uds_rule_config_t uds_rule_config0[9] =
{

    {
        /* Default Session Internal Process */
        .rxData = Inst0_rule_0_rxData,
        .rxMask = Inst0_rule_0_rxMask,
        .rxMatchLength = 1,
        .sessionMask =  UDS_SESSION_ALL | 0U,
        .secureLevelMask =  UDS_SECURITY_LEVEL_ALL | 0U,
        .callback = UDS_IP_SessionA,
    },
    {
        /* Default TesterPresent Internal Process */
        .rxData = Inst0_rule_1_rxData,
        .rxMask = Inst0_rule_1_rxMask,
        .rxMatchLength = 1,
        .sessionMask =  UDS_SESSION_ALL | 0U,
        .secureLevelMask =  UDS_SECURITY_LEVEL_ALL | 0U,
        .callback = UDS_IP_TesterPresentA,
    },
    {
        /* RoutineControl startRoutine EraseFlash */
        .rxData = Inst0_rule_2_rxData,
        .rxMask = Inst0_rule_2_rxMask,
        .rxMatchLength = 4,
        .sessionMask =  UDS_SESSION_PROGRAMMING | 0U,
        .secureLevelMask =  UDS_SECURITY_LEVEL_L1 |  UDS_SECURITY_LEVEL_NULL | 0U,
        .callback = UDS_IP_RoutineControlEraseFlashMemoryA,
    },
    {
        /* RequestDownload  */
        .rxData = Inst0_rule_3_rxData,
        .rxMask = Inst0_rule_3_rxMask,
        .rxMatchLength = 1,
        .sessionMask =  UDS_SESSION_PROGRAMMING | 0U,
        .secureLevelMask =  UDS_SECURITY_LEVEL_L1 |  UDS_SECURITY_LEVEL_NULL | 0U,
        .callback = UDS_IP_RequestDownloadA,
    },
    {
        /* TransferData  */
        .rxData = Inst0_rule_4_rxData,
        .rxMask = Inst0_rule_4_rxMask,
        .rxMatchLength = 1,
        .sessionMask =  UDS_SESSION_PROGRAMMING | 0U,
        .secureLevelMask =  UDS_SECURITY_LEVEL_L1 |  UDS_SECURITY_LEVEL_NULL | 0U,
        .callback = UDS_IP_TransferDataA,
    },
    {
        /* SecurityAccess  */
        .rxData = Inst0_rule_5_rxData,
        .rxMask = Inst0_rule_5_rxMask,
        .rxMatchLength = 1,
        .sessionMask =  UDS_SESSION_PROGRAMMING |  UDS_SESSION_DEFAULT | 0U,
        .secureLevelMask =  UDS_SECURITY_LEVEL_ALL |  UDS_SECURITY_LEVEL_NULL | 0U,
        .callback = UDS_IP_SecurityAccessA,
    },
    {
        /* RoutineControl startRoutine crcCheck */
        .rxData = Inst0_rule_6_rxData,
        .rxMask = Inst0_rule_6_rxMask,
        .rxMatchLength = 4,
        .sessionMask =  UDS_SESSION_PROGRAMMING | 0U,
        .secureLevelMask =  UDS_SECURITY_LEVEL_L1 |  UDS_SECURITY_LEVEL_NULL | 0U,
        .callback = UDS_IP_RoutineControlCrcCheckA,
    },
    {
        /* ECUReset SoftReset */
        .rxData = Inst0_rule_7_rxData,
        .rxMask = Inst0_rule_7_rxMask,
        .rxMatchLength = 1,
        .sessionMask =  UDS_SESSION_DEFAULT |  UDS_SESSION_PROGRAMMING | 0U,
        .secureLevelMask =  UDS_SECURITY_LEVEL_NULL |  UDS_SECURITY_LEVEL_L1 | 0U,
        .callback = UDS_IP_ECUResetSoftResetA,
    },
    {
        /* RequestTransferExit  */
        .rxData = Inst0_rule_8_rxData,
        .rxMask = Inst0_rule_8_rxMask,
        .rxMatchLength = 1,
        .sessionMask =  UDS_SESSION_PROGRAMMING | 0U,
        .secureLevelMask =  UDS_SECURITY_LEVEL_L1 |  UDS_SECURITY_LEVEL_NULL | 0U,
        .callback = UDS_IP_RequestTransferExitA,
    },
};

extern void udsRxCallback(uds_channel_t channel, uds_u8_t* data, uds_u16_t dataLength);

const uds_config_t uds_config[UDS_CHANNEL_NUM] = {
    {
        .channel = 0,
        .channelType = UDS_LINTP,
        .channelId = LIN_IFC_LinConfig_0,
        .s3ServerTime = 5000,
        .bufferSize = 512,
        .ruleConfigNum = 9,
        .ruleConfig = uds_rule_config0,
        .errorCallback = NULL,
        .rxCallback = udsRxCallback,
    },
};

uds_u8_t* uds_receivedData[UDS_CHANNEL_NUM];
uds_u8_t* uds_transmitData[UDS_CHANNEL_NUM];

uds_u8_t uds_receivedData0[512];
uds_u8_t uds_transmitData0[512];

void Uds_ConfigInit(void)
{
    uds_receivedData[0] = uds_receivedData0;
    uds_transmitData[0] = uds_transmitData0;
}
