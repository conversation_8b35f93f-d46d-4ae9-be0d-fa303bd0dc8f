{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "uds_lin_bootloader", "targetIndexes": [0, 1, 2, 3, 4, 5, 6]}], "targets": [{"directoryIndex": 0, "id": "GENERATED_CONFIG_TARGET::@6890427a1f51a3e7e1df", "jsonFile": "target-GENERATED_CONFIG_TARGET-Debug-0575a5f0881f982df931.json", "name": "GENERATED_CONFIG_TARGET", "projectIndex": 0}, {"directoryIndex": 0, "id": "GENERATED_SDK_TARGET::@6890427a1f51a3e7e1df", "jsonFile": "target-GENERATED_SDK_TARGET-Debug-b8c579c38ae7a6def0a5.json", "name": "GENERATED_SDK_TARGET", "projectIndex": 0}, {"directoryIndex": 0, "id": "genbin::@6890427a1f51a3e7e1df", "jsonFile": "target-genbin-Debug-50e566fbc67c83317f7b.json", "name": "genbin", "projectIndex": 0}, {"directoryIndex": 0, "id": "genhex::@6890427a1f51a3e7e1df", "jsonFile": "target-genhex-Debug-89986bf47728a944d851.json", "name": "genhex", "projectIndex": 0}, {"directoryIndex": 0, "id": "genlist::@6890427a1f51a3e7e1df", "jsonFile": "target-genlist-Debug-0c6695de87fa75ec6415.json", "name": "genlist", "projectIndex": 0}, {"directoryIndex": 0, "id": "gens19::@6890427a1f51a3e7e1df", "jsonFile": "target-gens19-Debug-342dd0c656c0486aca69.json", "name": "gens19", "projectIndex": 0}, {"directoryIndex": 0, "id": "uds_lin_bootloader.elf::@6890427a1f51a3e7e1df", "jsonFile": "target-uds_lin_bootloader.elf-Debug-bcd306bca03044974767.json", "name": "uds_lin_bootloader.elf", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "source": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader"}, "version": {"major": 2, "minor": 5}}