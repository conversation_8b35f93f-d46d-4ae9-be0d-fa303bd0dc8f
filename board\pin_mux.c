/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file pin_mux.c
 * @brief 
 * 
 */



#include "pin_mux.h"

pin_settings_config_t g_pin_mux_InitConfigArr0[NUM_OF_CONFIGURED_PINS0] = {
    /*PTB_0-54-LINFlexD0_RX-*/
    {
        .base=PCTRLB,
        .pinPortIdx = 0U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_LOW_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT2,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTB_1-53-LINFlexD0_TX-*/
    {
        .base=PCTRLB,
        .pinPortIdx = 1U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_LOW_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_ALT2,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_INPUT_DIRECTION,
        .initValue=0,
    },
    /*PTC_10-52-GPIO-*/
    {
        .base=PCTRLC,
        .pinPortIdx = 10U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_LOW_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOC,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=1,
    },
    /*PTB_4-28-GPIO-*/
    {
        .base=PCTRLB,
        .pinPortIdx = 4U,
        .pullConfig = PCTRL_INTERNAL_PULL_NOT_ENABLED,
        .driveSelect = PCTRL_LOW_DRIVE_STRENGTH,
        .passiveFilter = false,
        .mux = PCTRL_MUX_AS_GPIO,
        .intConfig = PCTRL_DMA_INT_DISABLED,
        .rateSelect = PCTRL_FAST_SLEW_RATE,
        .clearIntFlag = false,
        .digitalFilter = false,
        .gpioBase = GPIOB,
        .direction=GPIO_OUTPUT_DIRECTION,
        .initValue=0,
    },
};


/***********************************************************************************************************************
 * EOF
 **********************************************************************************************************************/

