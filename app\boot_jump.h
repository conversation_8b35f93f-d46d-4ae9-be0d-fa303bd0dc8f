

#ifndef  __BOOT_JUMPR_
#define  __BOOT_JUMPR_

#include "stdint.h"
#include "string.h"

#define  MCU_SECTOR_SIZE    0x400
#define  APP_IMAGE_START   	0xA000
#define  APP_IMAGE_LEN_MAX  (0x7FFFF + 1 - APP_IMAGE_START)

extern volatile uint32_t appEntry, appStack;
extern volatile uint32_t ECU_m_num_SysTick;
extern volatile uint32_t DCM_m_t_PollingTimer;
extern volatile uint32_t DCM_m_t_StayInTimer;


extern void System_Reset(void);
extern void bootup_application(uint32_t appEntry, uint32_t appStack);
extern void JumpTo_Application(void);
extern void JumpTo_Bootloader(void);

extern void StayInBoot_Init(void);
extern void StayInBoot_Task(void);

#endif
