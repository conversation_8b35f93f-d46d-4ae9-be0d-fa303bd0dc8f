
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake:199 (message)"
      - "CMakeLists.txt:6 (project)"
    message: |
      The target system is: Linux -  - arm
      The host system is: Windows - 10.0.22631 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc.exe 
      Build flags: -mcpu=cortex-m33
      Id flags:  
      
      The output was:
      1
      c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/ld.exe: c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\\libc.a(lib_a-exit.o): in function `exit':
      exit.c:(.text.exit+0x16): undefined reference to `_exit'
      collect2.exe: error: ld returned 1 exit status
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc.exe 
      Build flags: -mcpu=cortex-m33
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"
      
      The C compiler identification is GNU, found in:
        D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/CMakeFiles/3.26.4/CompilerIdC/CMakeCCompilerId.o
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++.exe 
      Build flags: -mcpu=cortex-m33
      Id flags:  
      
      The output was:
      1
      c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/ld.exe: c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp\\libc.a(lib_a-exit.o): in function `exit':
      exit.c:(.text.exit+0x16): undefined reference to `_exit'
      collect2.exe: error: ld returned 1 exit status
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++.exe 
      Build flags: -mcpu=cortex-m33
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"
      
      The CXX compiler identification is GNU, found in:
        D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/CMakeFiles/3.26.4/CompilerIdCXX/CMakeCXXCompilerId.o
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/CMakeFiles/CMakeScratch/TryCompile-ue3tsh"
      binary: "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/CMakeFiles/CMakeScratch/TryCompile-ue3tsh"
    cmakeVariables:
      CMAKE_C_FLAGS: "-mcpu=cortex-m33"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "-mcpu=cortex-m33"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/CMakeFiles/CMakeScratch/TryCompile-ue3tsh
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_9bb96 && [1/2] C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe   -mcpu=cortex-m33    -v -o CMakeFiles/cmTC_9bb96.dir/CMakeCCompilerABI.c.o -c C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe
        Target: arm-none-eabi
        Configured with: /mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/src/gcc/configure --build=x86_64-linux-gnu --host=i686-w64-mingw32 --target=arm-none-eabi --prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw --libexecdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/lib --infodir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/info --mandir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/man --htmldir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/html --pdfdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/pdf --enable-languages=c,c++ --enable-mingw-wildcard --disable-decimal-float --disable-libffi --disable-libgomp --disable-libmudflap --disable-libquadmath --disable-libssp --disable-libstdcxx-pch --disable-nls --disable-shared --disable-threads --disable-tls --with-gnu-as --with-gnu-ld --with-headers=yes --with-newlib --with-python-dir=share/gcc-arm-none-eabi --with-sysroot=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/arm-none-eabi --with-libiconv-prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-gmp=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-mpfr=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-mpc=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-isl=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-libelf=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-host-libstdcxx='-static-libgcc -Wl,-Bstatic,-lstdc++,-Bdynamic -lm' --with-pkgversion='GNU Arm Embedded Toolchain 10.3-2021.10' --with-multilib-list=rmprofile,aprofile
        Thread model: single
        Supported LTO compression algorithms: zlib
        gcc version 10.3.1 20210824 (release) (GNU Arm Embedded Toolchain 10.3-2021.10) 
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m33' '-v' '-o' 'CMakeFiles/cmTC_9bb96.dir/CMakeCCompilerABI.c.o' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv8-m.main+dsp' '-march=armv8-m.main+dsp'
         c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/cc1.exe -quiet -v -imultilib thumb/v8-m.main/nofp -iprefix c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/ -isysroot c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../arm-none-eabi -D__USES_INITFINI__ C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mcpu=cortex-m33 -mfloat-abi=soft -mthumb -mlibarch=armv8-m.main+dsp -march=armv8-m.main+dsp -auxbase-strip CMakeFiles/cmTC_9bb96.dir/CMakeCCompilerABI.c.o -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccXyFBXb.s
        GNU C17 (GNU Arm Embedded Toolchain 10.3-2021.10) version 10.3.1 20210824 (release) (arm-none-eabi)
        	compiled by GNU C version 7.3-win32 20180312, GMP version 6.1.0, MPFR version 3.1.4, MPC version 1.0.3, isl version isl-0.18-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/include"
        ignoring nonexistent directory "c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../arm-none-eabi/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/lib/gcc/arm-none-eabi/10.3.1/../../../../include"
        ignoring duplicate directory "c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/include-fixed"
        ignoring duplicate directory "c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include"
        ignoring nonexistent directory "c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../arm-none-eabi/usr/include"
        #include "..." search starts here:
        #include <...> search starts here:
         c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/include
         c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/include-fixed
         c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include
        End of search list.
        GNU C17 (GNU Arm Embedded Toolchain 10.3-2021.10) version 10.3.1 20210824 (release) (arm-none-eabi)
        	compiled by GNU C version 7.3-win32 20180312, GMP version 6.1.0, MPFR version 3.1.4, MPC version 1.0.3, isl version isl-0.18-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: f3937ce18b4177bfd408ca565336596a
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m33' '-v' '-o' 'CMakeFiles/cmTC_9bb96.dir/CMakeCCompilerABI.c.o' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv8-m.main+dsp' '-march=armv8-m.main+dsp'
         c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/as.exe -v -march=armv8-m.main+dsp -mfloat-abi=soft -meabi=5 -o CMakeFiles/cmTC_9bb96.dir/CMakeCCompilerABI.c.o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccXyFBXb.s
        GNU assembler version 2.36.1 (arm-none-eabi) using BFD version (GNU Arm Embedded Toolchain 10.3-2021.10) 2.36.1.20210621
        COMPILER_PATH=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/
        LIBRARY_PATH=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v8-m.main/nofp/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m33' '-v' '-o' 'CMakeFiles/cmTC_9bb96.dir/CMakeCCompilerABI.c.o' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv8-m.main+dsp' '-march=armv8-m.main+dsp'
        [2/2] cmd.exe /C "cd . && C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-g++.exe -mcpu=cortex-m33 -v   --specs=nosys.specs -Wl,--start-group -o cmTC_9bb96 CMakeFiles/cmTC_9bb96.dir/CMakeCCompilerABI.c.o   && cd ."
        Using built-in specs.
        Reading specs from c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/nosys.specs
        rename spec link_gcc_c_sequence to nosys_link_gcc_c_sequence
        COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-g++.exe
        COLLECT_LTO_WRAPPER=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/lto-wrapper.exe
        Target: arm-none-eabi
        Configured with: /mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/src/gcc/configure --build=x86_64-linux-gnu --host=i686-w64-mingw32 --target=arm-none-eabi --prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw --libexecdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/lib --infodir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/info --mandir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/man --htmldir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/html --pdfdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/pdf --enable-languages=c,c++ --enable-mingw-wildcard --disable-decimal-float --disable-libffi --disable-libgomp --disable-libmudflap --disable-libquadmath --disable-libssp --disable-libstdcxx-pch --disable-nls --disable-shared --disable-threads --disable-tls --with-gnu-as --with-gnu-ld --with-headers=yes --with-newlib --with-python-dir=share/gcc-arm-none-eabi --with-sysroot=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/arm-none-eabi --with-libiconv-prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-gmp=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-mpfr=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-mpc=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-isl=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-libelf=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-host-libstdcxx='-static-libgcc -Wl,-Bstatic,-lstdc++,-Bdynamic -lm' --with-pkgversion='GNU Arm Embedded Toolchain 10.3-2021.10' --with-multilib-list=rmprofile,aprofile
        Thread model: single
        Supported LTO compression algorithms: zlib
        gcc version 10.3.1 20210824 (release) (GNU Arm Embedded Toolchain 10.3-2021.10) 
        COMPILER_PATH=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/
        LIBRARY_PATH=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v8-m.main/nofp/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m33' '-v' '-specs=nosys.specs' '-o' 'cmTC_9bb96' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv8-m.main+dsp' '-march=armv8-m.main+dsp'
         c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/collect2.exe -plugin c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/liblto_plugin-0.dll -plugin-opt=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccb2nOOk.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lnosys --sysroot=c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../arm-none-eabi -X -o cmTC_9bb96 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crti.o c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtbegin.o c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp/crt0.o -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v8-m.main/nofp -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1 -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib --start-group CMakeFiles/cmTC_9bb96.dir/CMakeCCompilerABI.c.o -lstdc++ -lm --start-group -lgcc -lc --end-group --start-group -lgcc -lc -lnosys --end-group c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtend.o c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtn.o\x0d
        c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/ld.exe: missing --end-group; added as last command line option\x0d
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m33' '-v' '-specs=nosys.specs' '-o' 'cmTC_9bb96' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv8-m.main+dsp' '-march=armv8-m.main+dsp'\x0d
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineCompilerABI.cmake:127 (message)"
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include]
          add: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include-fixed]
          add: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include]
        end of search list found
        collapse include dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include]
        collapse include dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include-fixed] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include-fixed]
        collapse include dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include]
        implicit include dirs: [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include-fixed;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineCompilerABI.cmake:152 (message)"
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(arm-none-eabi-g\\+\\+\\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/CMakeFiles/CMakeScratch/TryCompile-ue3tsh]
        ignore line: []
        ignore line: [Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_9bb96 && [1/2] C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe   -mcpu=cortex-m33    -v -o CMakeFiles/cmTC_9bb96.dir/CMakeCCompilerABI.c.o -c C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe]
        ignore line: [Target: arm-none-eabi]
        ignore line: [Configured with: /mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/src/gcc/configure --build=x86_64-linux-gnu --host=i686-w64-mingw32 --target=arm-none-eabi --prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw --libexecdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/lib --infodir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/info --mandir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/man --htmldir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/html --pdfdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/pdf --enable-languages=c,c++ --enable-mingw-wildcard --disable-decimal-float --disable-libffi --disable-libgomp --disable-libmudflap --disable-libquadmath --disable-libssp --disable-libstdcxx-pch --disable-nls --disable-shared --disable-threads --disable-tls --with-gnu-as --with-gnu-ld --with-headers=yes --with-newlib --with-python-dir=share/gcc-arm-none-eabi --with-sysroot=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/arm-none-eabi --with-libiconv-prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-gmp=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-mpfr=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-mpc=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-isl=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-libelf=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-host-libstdcxx='-static-libgcc -Wl,-Bstatic,-lstdc++,-Bdynamic -lm' --with-pkgversion='GNU Arm Embedded Toolchain 10.3-2021.10' --with-multilib-list=rmprofile,aprofile]
        ignore line: [Thread model: single]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 10.3.1 20210824 (release) (GNU Arm Embedded Toolchain 10.3-2021.10) ]
        ignore line: [COLLECT_GCC_OPTIONS='-mcpu=cortex-m33' '-v' '-o' 'CMakeFiles/cmTC_9bb96.dir/CMakeCCompilerABI.c.o' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv8-m.main+dsp' '-march=armv8-m.main+dsp']
        ignore line: [ c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/cc1.exe -quiet -v -imultilib thumb/v8-m.main/nofp -iprefix c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/ -isysroot c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../arm-none-eabi -D__USES_INITFINI__ C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mcpu=cortex-m33 -mfloat-abi=soft -mthumb -mlibarch=armv8-m.main+dsp -march=armv8-m.main+dsp -auxbase-strip CMakeFiles/cmTC_9bb96.dir/CMakeCCompilerABI.c.o -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccXyFBXb.s]
        ignore line: [GNU C17 (GNU Arm Embedded Toolchain 10.3-2021.10) version 10.3.1 20210824 (release) (arm-none-eabi)]
        ignore line: [	compiled by GNU C version 7.3-win32 20180312  GMP version 6.1.0  MPFR version 3.1.4  MPC version 1.0.3  isl version isl-0.18-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/include"]
        ignore line: [ignoring nonexistent directory "c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../arm-none-eabi/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/lib/gcc/arm-none-eabi/10.3.1/../../../../include"]
        ignore line: [ignoring duplicate directory "c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/include-fixed"]
        ignore line: [ignoring duplicate directory "c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include"]
        ignore line: [ignoring nonexistent directory "c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../arm-none-eabi/usr/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/include]
        ignore line: [ c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/include-fixed]
        ignore line: [ c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include]
        ignore line: [End of search list.]
        ignore line: [GNU C17 (GNU Arm Embedded Toolchain 10.3-2021.10) version 10.3.1 20210824 (release) (arm-none-eabi)]
        ignore line: [	compiled by GNU C version 7.3-win32 20180312  GMP version 6.1.0  MPFR version 3.1.4  MPC version 1.0.3  isl version isl-0.18-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: f3937ce18b4177bfd408ca565336596a]
        ignore line: [COLLECT_GCC_OPTIONS='-mcpu=cortex-m33' '-v' '-o' 'CMakeFiles/cmTC_9bb96.dir/CMakeCCompilerABI.c.o' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv8-m.main+dsp' '-march=armv8-m.main+dsp']
        ignore line: [ c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/as.exe -v -march=armv8-m.main+dsp -mfloat-abi=soft -meabi=5 -o CMakeFiles/cmTC_9bb96.dir/CMakeCCompilerABI.c.o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccXyFBXb.s]
        ignore line: [GNU assembler version 2.36.1 (arm-none-eabi) using BFD version (GNU Arm Embedded Toolchain 10.3-2021.10) 2.36.1.20210621]
        ignore line: [COMPILER_PATH=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/]
        ignore line: [LIBRARY_PATH=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v8-m.main/nofp/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-mcpu=cortex-m33' '-v' '-o' 'CMakeFiles/cmTC_9bb96.dir/CMakeCCompilerABI.c.o' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv8-m.main+dsp' '-march=armv8-m.main+dsp']
        ignore line: [[2/2] cmd.exe /C "cd . && C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-g++.exe -mcpu=cortex-m33 -v   --specs=nosys.specs -Wl,--start-group -o cmTC_9bb96 CMakeFiles/cmTC_9bb96.dir/CMakeCCompilerABI.c.o   && cd ."]
        ignore line: [Using built-in specs.]
        ignore line: [Reading specs from c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/nosys.specs]
        ignore line: [rename spec link_gcc_c_sequence to nosys_link_gcc_c_sequence]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-g++.exe]
        ignore line: [COLLECT_LTO_WRAPPER=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/lto-wrapper.exe]
        ignore line: [Target: arm-none-eabi]
        ignore line: [Configured with: /mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/src/gcc/configure --build=x86_64-linux-gnu --host=i686-w64-mingw32 --target=arm-none-eabi --prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw --libexecdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/lib --infodir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/info --mandir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/man --htmldir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/html --pdfdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/pdf --enable-languages=c,c++ --enable-mingw-wildcard --disable-decimal-float --disable-libffi --disable-libgomp --disable-libmudflap --disable-libquadmath --disable-libssp --disable-libstdcxx-pch --disable-nls --disable-shared --disable-threads --disable-tls --with-gnu-as --with-gnu-ld --with-headers=yes --with-newlib --with-python-dir=share/gcc-arm-none-eabi --with-sysroot=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/arm-none-eabi --with-libiconv-prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-gmp=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-mpfr=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-mpc=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-isl=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-libelf=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-host-libstdcxx='-static-libgcc -Wl,-Bstatic,-lstdc++,-Bdynamic -lm' --with-pkgversion='GNU Arm Embedded Toolchain 10.3-2021.10' --with-multilib-list=rmprofile,aprofile]
        ignore line: [Thread model: single]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 10.3.1 20210824 (release) (GNU Arm Embedded Toolchain 10.3-2021.10) ]
        ignore line: [COMPILER_PATH=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/]
        ignore line: [LIBRARY_PATH=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v8-m.main/nofp/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-mcpu=cortex-m33' '-v' '-specs=nosys.specs' '-o' 'cmTC_9bb96' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv8-m.main+dsp' '-march=armv8-m.main+dsp']
        link line: [ c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/collect2.exe -plugin c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/liblto_plugin-0.dll -plugin-opt=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccb2nOOk.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lnosys --sysroot=c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../arm-none-eabi -X -o cmTC_9bb96 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crti.o c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtbegin.o c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp/crt0.o -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v8-m.main/nofp -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1 -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib --start-group CMakeFiles/cmTC_9bb96.dir/CMakeCCompilerABI.c.o -lstdc++ -lm --start-group -lgcc -lc --end-group --start-group -lgcc -lc -lnosys --end-group c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtend.o c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtn.o\x0d]
          arg [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/liblto_plugin-0.dll] ==> ignore
          arg [-plugin-opt=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccb2nOOk.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lnosys] ==> ignore
          arg [--sysroot=c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../arm-none-eabi] ==> ignore
          arg [-X] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_9bb96] ==> ignore
          arg [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crti.o] ==> obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crti.o]
          arg [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtbegin.o] ==> obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtbegin.o]
          arg [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp/crt0.o] ==> obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp/crt0.o]
          arg [-Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp] ==> dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp]
          arg [-Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp] ==> dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp]
          arg [-Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v8-m.main/nofp] ==> dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v8-m.main/nofp]
          arg [-Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1] ==> dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1]
          arg [-Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc] ==> dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc]
          arg [-Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib] ==> dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib]
          arg [-Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib] ==> dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib]
          arg [--start-group] ==> ignore
          arg [CMakeFiles/cmTC_9bb96.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lm] ==> lib [m]
          arg [--start-group] ==> ignore
          arg [-lgcc] ==> lib [gcc]
          arg [-lc] ==> lib [c]
          arg [--end-group] ==> ignore
          arg [--start-group] ==> ignore
          arg [-lgcc] ==> lib [gcc]
          arg [-lc] ==> lib [c]
          arg [-lnosys] ==> lib [nosys]
          arg [--end-group] ==> ignore
          arg [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtend.o] ==> obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtend.o]
          arg [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtn.o] ==> obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtn.o]
        collapse obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crti.o] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crti.o]
        collapse obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtbegin.o] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtbegin.o]
        collapse obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp/crt0.o] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib/thumb/v8-m.main/nofp/crt0.o]
        collapse obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtend.o] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtend.o]
        collapse obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtn.o] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtn.o]
        collapse library dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp]
        collapse library dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib/thumb/v8-m.main/nofp]
        collapse library dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v8-m.main/nofp] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib/thumb/v8-m.main/nofp]
        collapse library dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1]
        collapse library dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc]
        collapse library dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib]
        collapse library dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib]
        implicit libs: [stdc++;m;gcc;c;gcc;c;nosys]
        implicit objs: [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crti.o;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtbegin.o;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib/thumb/v8-m.main/nofp/crt0.o;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtend.o;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtn.o]
        implicit dirs: [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib/thumb/v8-m.main/nofp;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib]
        implicit fwks: []
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/CMakeFiles/CMakeScratch/TryCompile-amu0ec"
      binary: "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/CMakeFiles/CMakeScratch/TryCompile-amu0ec"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-mcpu=cortex-m33"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "-mcpu=cortex-m33"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/CMakeFiles/CMakeScratch/TryCompile-amu0ec
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_3bd48 && [1/2] C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-g++.exe   -mcpu=cortex-m33    -v -o CMakeFiles/cmTC_3bd48.dir/CMakeCXXCompilerABI.cpp.o -c C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-g++.exe
        Target: arm-none-eabi
        Configured with: /mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/src/gcc/configure --build=x86_64-linux-gnu --host=i686-w64-mingw32 --target=arm-none-eabi --prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw --libexecdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/lib --infodir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/info --mandir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/man --htmldir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/html --pdfdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/pdf --enable-languages=c,c++ --enable-mingw-wildcard --disable-decimal-float --disable-libffi --disable-libgomp --disable-libmudflap --disable-libquadmath --disable-libssp --disable-libstdcxx-pch --disable-nls --disable-shared --disable-threads --disable-tls --with-gnu-as --with-gnu-ld --with-headers=yes --with-newlib --with-python-dir=share/gcc-arm-none-eabi --with-sysroot=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/arm-none-eabi --with-libiconv-prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-gmp=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-mpfr=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-mpc=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-isl=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-libelf=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-host-libstdcxx='-static-libgcc -Wl,-Bstatic,-lstdc++,-Bdynamic -lm' --with-pkgversion='GNU Arm Embedded Toolchain 10.3-2021.10' --with-multilib-list=rmprofile,aprofile
        Thread model: single
        Supported LTO compression algorithms: zlib
        gcc version 10.3.1 20210824 (release) (GNU Arm Embedded Toolchain 10.3-2021.10) 
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m33' '-v' '-o' 'CMakeFiles/cmTC_3bd48.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv8-m.main+dsp' '-march=armv8-m.main+dsp'
         c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/cc1plus.exe -quiet -v -imultilib thumb/v8-m.main/nofp -iprefix c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/ -isysroot c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../arm-none-eabi -D__USES_INITFINI__ C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mcpu=cortex-m33 -mfloat-abi=soft -mthumb -mlibarch=armv8-m.main+dsp -march=armv8-m.main+dsp -auxbase-strip CMakeFiles/cmTC_3bd48.dir/CMakeCXXCompilerABI.cpp.o -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccyNdTnk.s
        GNU C++14 (GNU Arm Embedded Toolchain 10.3-2021.10) version 10.3.1 20210824 (release) (arm-none-eabi)
        	compiled by GNU C version 7.3-win32 20180312, GMP version 6.1.0, MPFR version 3.1.4, MPC version 1.0.3, isl version isl-0.18-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1"
        ignoring duplicate directory "c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/arm-none-eabi/thumb/v8-m.main/nofp"
        ignoring duplicate directory "c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/backward"
        ignoring duplicate directory "c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/include"
        ignoring nonexistent directory "c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../arm-none-eabi/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/lib/gcc/arm-none-eabi/10.3.1/../../../../include"
        ignoring duplicate directory "c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/include-fixed"
        ignoring duplicate directory "c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include"
        ignoring nonexistent directory "c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../arm-none-eabi/usr/include"
        #include "..." search starts here:
        #include <...> search starts here:
         c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1
         c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/arm-none-eabi/thumb/v8-m.main/nofp
         c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/backward
         c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/include
         c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/include-fixed
         c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include
        End of search list.
        GNU C++14 (GNU Arm Embedded Toolchain 10.3-2021.10) version 10.3.1 20210824 (release) (arm-none-eabi)
        	compiled by GNU C version 7.3-win32 20180312, GMP version 6.1.0, MPFR version 3.1.4, MPC version 1.0.3, isl version isl-0.18-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: f8787892a7c5aa84cea58dce52be7118
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m33' '-v' '-o' 'CMakeFiles/cmTC_3bd48.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv8-m.main+dsp' '-march=armv8-m.main+dsp'
         c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/as.exe -v -march=armv8-m.main+dsp -mfloat-abi=soft -meabi=5 -o CMakeFiles/cmTC_3bd48.dir/CMakeCXXCompilerABI.cpp.o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccyNdTnk.s
        GNU assembler version 2.36.1 (arm-none-eabi) using BFD version (GNU Arm Embedded Toolchain 10.3-2021.10) 2.36.1.20210621
        COMPILER_PATH=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/
        LIBRARY_PATH=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v8-m.main/nofp/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/\x0d
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m33' '-v' '-o' 'CMakeFiles/cmTC_3bd48.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv8-m.main+dsp' '-march=armv8-m.main+dsp'\x0d
        [2/2] cmd.exe /C "cd . && C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-g++.exe -mcpu=cortex-m33 -v   --specs=nosys.specs -Wl,--start-group -o cmTC_3bd48 CMakeFiles/cmTC_3bd48.dir/CMakeCXXCompilerABI.cpp.o   && cd ."
        Using built-in specs.
        Reading specs from c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/nosys.specs
        rename spec link_gcc_c_sequence to nosys_link_gcc_c_sequence
        COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-g++.exe
        COLLECT_LTO_WRAPPER=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/lto-wrapper.exe
        Target: arm-none-eabi
        Configured with: /mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/src/gcc/configure --build=x86_64-linux-gnu --host=i686-w64-mingw32 --target=arm-none-eabi --prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw --libexecdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/lib --infodir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/info --mandir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/man --htmldir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/html --pdfdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/pdf --enable-languages=c,c++ --enable-mingw-wildcard --disable-decimal-float --disable-libffi --disable-libgomp --disable-libmudflap --disable-libquadmath --disable-libssp --disable-libstdcxx-pch --disable-nls --disable-shared --disable-threads --disable-tls --with-gnu-as --with-gnu-ld --with-headers=yes --with-newlib --with-python-dir=share/gcc-arm-none-eabi --with-sysroot=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/arm-none-eabi --with-libiconv-prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-gmp=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-mpfr=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-mpc=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-isl=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-libelf=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-host-libstdcxx='-static-libgcc -Wl,-Bstatic,-lstdc++,-Bdynamic -lm' --with-pkgversion='GNU Arm Embedded Toolchain 10.3-2021.10' --with-multilib-list=rmprofile,aprofile
        Thread model: single
        Supported LTO compression algorithms: zlib
        gcc version 10.3.1 20210824 (release) (GNU Arm Embedded Toolchain 10.3-2021.10) 
        COMPILER_PATH=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/
        LIBRARY_PATH=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v8-m.main/nofp/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/;c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m33' '-v' '-specs=nosys.specs' '-o' 'cmTC_3bd48' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv8-m.main+dsp' '-march=armv8-m.main+dsp'
         c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/collect2.exe -plugin c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/liblto_plugin-0.dll -plugin-opt=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccUOLQwo.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lnosys --sysroot=c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../arm-none-eabi -X -o cmTC_3bd48 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crti.o c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtbegin.o c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp/crt0.o -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v8-m.main/nofp -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1 -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib --start-group CMakeFiles/cmTC_3bd48.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm --start-group -lgcc -lc --end-group --start-group -lgcc -lc -lnosys --end-group c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtend.o c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtn.o
        c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/ld.exe: missing --end-group; added as last command line option
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m33' '-v' '-specs=nosys.specs' '-o' 'cmTC_3bd48' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv8-m.main+dsp' '-march=armv8-m.main+dsp'\x0d
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineCompilerABI.cmake:127 (message)"
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1]
          add: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/arm-none-eabi/thumb/v8-m.main/nofp]
          add: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/backward]
          add: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include]
          add: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include-fixed]
          add: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include]
        end of search list found
        collapse include dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include/c++/10.3.1]
        collapse include dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/arm-none-eabi/thumb/v8-m.main/nofp] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include/c++/10.3.1/arm-none-eabi/thumb/v8-m.main/nofp]
        collapse include dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/backward] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include/c++/10.3.1/backward]
        collapse include dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include]
        collapse include dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/include-fixed] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include-fixed]
        collapse include dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include]
        implicit include dirs: [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include/c++/10.3.1;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include/c++/10.3.1/arm-none-eabi/thumb/v8-m.main/nofp;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include/c++/10.3.1/backward;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include-fixed;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineCompilerABI.cmake:152 (message)"
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(arm-none-eabi-g\\+\\+\\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build/CMakeFiles/CMakeScratch/TryCompile-amu0ec]
        ignore line: []
        ignore line: [Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_3bd48 && [1/2] C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-g++.exe   -mcpu=cortex-m33    -v -o CMakeFiles/cmTC_3bd48.dir/CMakeCXXCompilerABI.cpp.o -c C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-g++.exe]
        ignore line: [Target: arm-none-eabi]
        ignore line: [Configured with: /mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/src/gcc/configure --build=x86_64-linux-gnu --host=i686-w64-mingw32 --target=arm-none-eabi --prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw --libexecdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/lib --infodir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/info --mandir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/man --htmldir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/html --pdfdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/pdf --enable-languages=c,c++ --enable-mingw-wildcard --disable-decimal-float --disable-libffi --disable-libgomp --disable-libmudflap --disable-libquadmath --disable-libssp --disable-libstdcxx-pch --disable-nls --disable-shared --disable-threads --disable-tls --with-gnu-as --with-gnu-ld --with-headers=yes --with-newlib --with-python-dir=share/gcc-arm-none-eabi --with-sysroot=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/arm-none-eabi --with-libiconv-prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-gmp=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-mpfr=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-mpc=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-isl=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-libelf=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-host-libstdcxx='-static-libgcc -Wl,-Bstatic,-lstdc++,-Bdynamic -lm' --with-pkgversion='GNU Arm Embedded Toolchain 10.3-2021.10' --with-multilib-list=rmprofile,aprofile]
        ignore line: [Thread model: single]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 10.3.1 20210824 (release) (GNU Arm Embedded Toolchain 10.3-2021.10) ]
        ignore line: [COLLECT_GCC_OPTIONS='-mcpu=cortex-m33' '-v' '-o' 'CMakeFiles/cmTC_3bd48.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv8-m.main+dsp' '-march=armv8-m.main+dsp']
        ignore line: [ c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/cc1plus.exe -quiet -v -imultilib thumb/v8-m.main/nofp -iprefix c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/ -isysroot c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../arm-none-eabi -D__USES_INITFINI__ C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mcpu=cortex-m33 -mfloat-abi=soft -mthumb -mlibarch=armv8-m.main+dsp -march=armv8-m.main+dsp -auxbase-strip CMakeFiles/cmTC_3bd48.dir/CMakeCXXCompilerABI.cpp.o -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccyNdTnk.s]
        ignore line: [GNU C++14 (GNU Arm Embedded Toolchain 10.3-2021.10) version 10.3.1 20210824 (release) (arm-none-eabi)]
        ignore line: [	compiled by GNU C version 7.3-win32 20180312  GMP version 6.1.0  MPFR version 3.1.4  MPC version 1.0.3  isl version isl-0.18-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1"]
        ignore line: [ignoring duplicate directory "c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/arm-none-eabi/thumb/v8-m.main/nofp"]
        ignore line: [ignoring duplicate directory "c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/backward"]
        ignore line: [ignoring duplicate directory "c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/include"]
        ignore line: [ignoring nonexistent directory "c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../arm-none-eabi/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/lib/gcc/arm-none-eabi/10.3.1/../../../../include"]
        ignore line: [ignoring duplicate directory "c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/include-fixed"]
        ignore line: [ignoring duplicate directory "c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/../../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include"]
        ignore line: [ignoring nonexistent directory "c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../arm-none-eabi/usr/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1]
        ignore line: [ c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/arm-none-eabi/thumb/v8-m.main/nofp]
        ignore line: [ c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include/c++/10.3.1/backward]
        ignore line: [ c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/include]
        ignore line: [ c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/include-fixed]
        ignore line: [ c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/include]
        ignore line: [End of search list.]
        ignore line: [GNU C++14 (GNU Arm Embedded Toolchain 10.3-2021.10) version 10.3.1 20210824 (release) (arm-none-eabi)]
        ignore line: [	compiled by GNU C version 7.3-win32 20180312  GMP version 6.1.0  MPFR version 3.1.4  MPC version 1.0.3  isl version isl-0.18-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: f8787892a7c5aa84cea58dce52be7118]
        ignore line: [COLLECT_GCC_OPTIONS='-mcpu=cortex-m33' '-v' '-o' 'CMakeFiles/cmTC_3bd48.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv8-m.main+dsp' '-march=armv8-m.main+dsp']
        ignore line: [ c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/as.exe -v -march=armv8-m.main+dsp -mfloat-abi=soft -meabi=5 -o CMakeFiles/cmTC_3bd48.dir/CMakeCXXCompilerABI.cpp.o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccyNdTnk.s]
        ignore line: [GNU assembler version 2.36.1 (arm-none-eabi) using BFD version (GNU Arm Embedded Toolchain 10.3-2021.10) 2.36.1.20210621]
        ignore line: [COMPILER_PATH=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/]
        ignore line: [LIBRARY_PATH=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v8-m.main/nofp/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/\x0d]
        ignore line: [COLLECT_GCC_OPTIONS='-mcpu=cortex-m33' '-v' '-o' 'CMakeFiles/cmTC_3bd48.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv8-m.main+dsp' '-march=armv8-m.main+dsp'\x0d]
        ignore line: [[2/2] cmd.exe /C "cd . && C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-g++.exe -mcpu=cortex-m33 -v   --specs=nosys.specs -Wl,--start-group -o cmTC_3bd48 CMakeFiles/cmTC_3bd48.dir/CMakeCXXCompilerABI.cpp.o   && cd ."]
        ignore line: [Using built-in specs.]
        ignore line: [Reading specs from c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/nosys.specs]
        ignore line: [rename spec link_gcc_c_sequence to nosys_link_gcc_c_sequence]
        ignore line: [COLLECT_GCC=C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-g++.exe]
        ignore line: [COLLECT_LTO_WRAPPER=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/lto-wrapper.exe]
        ignore line: [Target: arm-none-eabi]
        ignore line: [Configured with: /mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/src/gcc/configure --build=x86_64-linux-gnu --host=i686-w64-mingw32 --target=arm-none-eabi --prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw --libexecdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/lib --infodir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/info --mandir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/man --htmldir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/html --pdfdir=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/share/doc/gcc-arm-none-eabi/pdf --enable-languages=c,c++ --enable-mingw-wildcard --disable-decimal-float --disable-libffi --disable-libgomp --disable-libmudflap --disable-libquadmath --disable-libssp --disable-libstdcxx-pch --disable-nls --disable-shared --disable-threads --disable-tls --with-gnu-as --with-gnu-ld --with-headers=yes --with-newlib --with-python-dir=share/gcc-arm-none-eabi --with-sysroot=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/install-mingw/arm-none-eabi --with-libiconv-prefix=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-gmp=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-mpfr=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-mpc=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-isl=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-libelf=/mnt/workspace/workspace/GCC-10-pipeline/jenkins-GCC-10-pipeline-338_20211018_1634516203/build-mingw/host-libs/usr --with-host-libstdcxx='-static-libgcc -Wl,-Bstatic,-lstdc++,-Bdynamic -lm' --with-pkgversion='GNU Arm Embedded Toolchain 10.3-2021.10' --with-multilib-list=rmprofile,aprofile]
        ignore line: [Thread model: single]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 10.3.1 20210824 (release) (GNU Arm Embedded Toolchain 10.3-2021.10) ]
        ignore line: [COMPILER_PATH=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/bin/]
        ignore line: [LIBRARY_PATH=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v8-m.main/nofp/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/]
        ignore line: [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-mcpu=cortex-m33' '-v' '-specs=nosys.specs' '-o' 'cmTC_3bd48' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv8-m.main+dsp' '-march=armv8-m.main+dsp']
        link line: [ c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/collect2.exe -plugin c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/liblto_plugin-0.dll -plugin-opt=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccUOLQwo.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lnosys --sysroot=c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../arm-none-eabi -X -o cmTC_3bd48 c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crti.o c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtbegin.o c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp/crt0.o -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v8-m.main/nofp -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1 -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib -Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib --start-group CMakeFiles/cmTC_3bd48.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm --start-group -lgcc -lc --end-group --start-group -lgcc -lc -lnosys --end-group c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtend.o c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtn.o]
          arg [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/liblto_plugin-0.dll] ==> ignore
          arg [-plugin-opt=c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccUOLQwo.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lnosys] ==> ignore
          arg [--sysroot=c:\\users\\<USER>\\appdata\\roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\../arm-none-eabi] ==> ignore
          arg [-X] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_3bd48] ==> ignore
          arg [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crti.o] ==> obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crti.o]
          arg [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtbegin.o] ==> obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtbegin.o]
          arg [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp/crt0.o] ==> obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp/crt0.o]
          arg [-Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp] ==> dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp]
          arg [-Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp] ==> dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp]
          arg [-Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v8-m.main/nofp] ==> dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v8-m.main/nofp]
          arg [-Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1] ==> dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1]
          arg [-Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc] ==> dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc]
          arg [-Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib] ==> dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib]
          arg [-Lc:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib] ==> dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib]
          arg [--start-group] ==> ignore
          arg [CMakeFiles/cmTC_3bd48.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lm] ==> lib [m]
          arg [--start-group] ==> ignore
          arg [-lgcc] ==> lib [gcc]
          arg [-lc] ==> lib [c]
          arg [--end-group] ==> ignore
          arg [--start-group] ==> ignore
          arg [-lgcc] ==> lib [gcc]
          arg [-lc] ==> lib [c]
          arg [-lnosys] ==> lib [nosys]
          arg [--end-group] ==> ignore
          arg [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtend.o] ==> obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtend.o]
          arg [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtn.o] ==> obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtn.o]
        collapse obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crti.o] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crti.o]
        collapse obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtbegin.o] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtbegin.o]
        collapse obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp/crt0.o] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib/thumb/v8-m.main/nofp/crt0.o]
        collapse obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtend.o] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtend.o]
        collapse obj [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtn.o] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtn.o]
        collapse library dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp]
        collapse library dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main/nofp] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib/thumb/v8-m.main/nofp]
        collapse library dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib/thumb/v8-m.main/nofp] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib/thumb/v8-m.main/nofp]
        collapse library dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1]
        collapse library dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc]
        collapse library dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib]
        collapse library dir [c:/users/<USER>/appdata/roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/../arm-none-eabi/lib] ==> [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib]
        implicit libs: [stdc++;m;gcc;c;gcc;c;nosys]
        implicit objs: [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crti.o;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtbegin.o;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib/thumb/v8-m.main/nofp/crt0.o;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtend.o;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp/crtn.o]
        implicit dirs: [C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib/thumb/v8-m.main/nofp;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc;C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:1102 (message)"
      - "C:/Users/<USER>/AppData/Roaming/yt_config_tool/cmake-3.26.4-windows-x86_64/share/cmake-3.26/Modules/CMakeDetermineASMCompiler.cmake:127 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "cmake/configLib.cmake:14 (enable_language)"
      - "cmake/config.cmake:4 (include)"
      - "CMakeLists.txt:16 (include)"
    message: |
      Checking whether the ASM compiler is GNU using "--version" matched "(GNU assembler)|(GCC)|(Free Software Foundation)":
      arm-none-eabi-gcc.exe (GNU Arm Embedded Toolchain 10.3-2021.10) 10.3.1 20210824 (release)
      Copyright (C) 2020 Free Software Foundation, Inc.
      This is free software; see the source for copying conditions.  There is NO
      warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
      
...
