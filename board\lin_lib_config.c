/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file lin_lib_config.c
 * @brief 
 * 
 */


#include "lin_lib_config.h"





/* =====  start of LinConfig_0 config ==== */



/* LinConfig_0 */
static const lin_signal_t LinConfig_0_signals[]={
/* Motor1_Dynamic_Sig*/
    {
        .signalType=LIN_SIGNAL_TYPE_SCALAR,
        .flagBitOffset=0,
        .bufOffset=0,
        .bitLen=8,
    },
    
    /* Motor1ErrorCode*/
    {
        .signalType=LIN_SIGNAL_TYPE_SCALAR,
        .flagBitOffset=1,
        .bufOffset=1,
        .bitLen=8,
    },
    
    /* Motor1ErrorValue*/
    {
        .signalType=LIN_SIGNAL_TYPE_SCALAR,
        .flagBitOffset=2,
        .bufOffset=2,
        .bitLen=8,
    },
    
    /* Motor1LinError*/
    {
        .signalType=LIN_SIGNAL_TYPE_SCALAR,
        .flagBitOffset=3,
        .bufOffset=3,
        .bitLen=1,
    },
    
    /* Motor1Position*/
    {
        .signalType=LIN_SIGNAL_TYPE_ARRAY,
        .flagBitOffset=4,
        .bufOffset=4,
        .bitLen=32,
    },
    
    /* Motor1Temp*/
    {
        .signalType=LIN_SIGNAL_TYPE_SCALAR,
        .flagBitOffset=5,
        .bufOffset=8,
        .bitLen=8,
    },
    
    /* MotorDirection*/
    {
        .signalType=LIN_SIGNAL_TYPE_SCALAR,
        .flagBitOffset=6,
        .bufOffset=9,
        .bitLen=2,
    },
    
    /* MotorSelection*/
    {
        .signalType=LIN_SIGNAL_TYPE_SCALAR,
        .flagBitOffset=7,
        .bufOffset=10,
        .bitLen=4,
    },
    
    /* MotorSpeed*/
    {
        .signalType=LIN_SIGNAL_TYPE_SCALAR,
        .flagBitOffset=8,
        .bufOffset=11,
        .bitLen=10,
    },
    
    };

static l_u8 LinConfig_0_data_pool[13] = {
/* Motor1_Dynamic_Sig*/
    7,
/* Motor1ErrorCode*/
    5,
/* Motor1ErrorValue*/
    1,
/* Motor1LinError*/
    0,
/* Motor1Position*/
    0,0,0,0,
/* Motor1Temp*/
    5,
/* MotorDirection*/
    0,
/* MotorSelection*/
    0,
/* MotorSpeed*/
    0, 0,
};
static l_u8 LinConfig_0_flag_pool[2];
static l_u8 LinConfig_0_update_pool[2];

/* const signal config */
/* frame Motor1_Dynamic signals table */
static const l_u8 LinConfig_0_Motor1_Dynamic_signals[]={
/* Motor1_Dynamic_Sig*/
    0,
};
/* frame Motor1State_Cycl signals table */
static const l_u8 LinConfig_0_Motor1State_Cycl_signals[]={
/* Motor1Temp*/
    5,
/* Motor1Position*/
    4,
/* Motor1LinError*/
    3,
/* Motor1_Dynamic_Sig*/
    0,
};
/* frame Motor1State_Event signals table */
static const l_u8 LinConfig_0_Motor1State_Event_signals[]={
/* Motor1ErrorCode*/
    1,
/* Motor1ErrorValue*/
    2,
};
/* frame MotorControl signals table */
static const l_u8 LinConfig_0_MotorControl_signals[]={
/* MotorDirection, offset*/
    6,
/* MotorSpeed, offset*/
    8,
/* MotorSelection, offset*/
    7,
};
/* frame Motor1_Dynamic signals table */
static const l_u8 LinConfig_0_Motor1_Dynamic_offsets[]={
/* Motor1_Dynamic_Sig*/
    0,
};
/* frame Motor1State_Cycl signals table */
static const l_u8 LinConfig_0_Motor1State_Cycl_offsets[]={
/* Motor1Temp*/
    0,
/* Motor1Position*/
    8,
/* Motor1LinError*/
    40,
/* Motor1_Dynamic_Sig*/
    41,
};
/* frame Motor1State_Event signals table */
static const l_u8 LinConfig_0_Motor1State_Event_offsets[]={
/* Motor1ErrorCode*/
    8,
/* Motor1ErrorValue*/
    16,
};
/* frame MotorControl signals table */
static const l_u8 LinConfig_0_MotorControl_offsets[]={
/* MotorDirection, offset*/
    0,
/* MotorSpeed, offset*/
    2,
/* MotorSelection, offset*/
    12,
};
/* const frame config */
static const lin_frame_t LinConfig_0_frames[]={
/* Motor1_Dynamic */
    {
        .frameType=LIN_FRAME_TYPE_UNCD,
        .bufLen=1,
        .flagBitOffset=0,
        .id=53,
        .dir=LIN_DIR_PUBLISH,
        .checksumType=LIN_FRAME_CHECKSUM_ENHANCED,
        .signalList=LinConfig_0_Motor1_Dynamic_signals,
        .signalOffset=LinConfig_0_Motor1_Dynamic_offsets,
        .signalNum=1,
        .idChangeable=l_true,
        .idRamIndex=5,
    },
    /* Motor1State_Cycl */
    {
        .frameType=LIN_FRAME_TYPE_UNCD,
        .bufLen=7,
        .flagBitOffset=1,
        .id=51,
        .dir=LIN_DIR_PUBLISH,
        .checksumType=LIN_FRAME_CHECKSUM_ENHANCED,
        .signalList=LinConfig_0_Motor1State_Cycl_signals,
        .signalOffset=LinConfig_0_Motor1State_Cycl_offsets,
        .signalNum=4,
        .idChangeable=l_true,
        .idRamIndex=2,
    },
    /* Motor1State_Event */
    {
        .frameType=LIN_FRAME_TYPE_UNCD,
        .bufLen=3,
        .flagBitOffset=2,
        .id=54,
        .dir=LIN_DIR_PUBLISH,
        .checksumType=LIN_FRAME_CHECKSUM_ENHANCED,
        .signalList=LinConfig_0_Motor1State_Event_signals,
        .signalOffset=LinConfig_0_Motor1State_Event_offsets,
        .signalNum=2,
        .idChangeable=l_true,
        .idRamIndex=3,
    },
    /* MotorControl */
    {
        .frameType=LIN_FRAME_TYPE_UNCD,
        .bufLen=2,
        .flagBitOffset=3,
        .id=45,
        .dir=LIN_DIR_SUBSCRIBE,
        .checksumType=LIN_FRAME_CHECKSUM_ENHANCED,
        .signalList=LinConfig_0_MotorControl_signals,
        .signalOffset=LinConfig_0_MotorControl_offsets,
        .signalNum=3,
        .idChangeable=l_true,
        .idRamIndex=1,
    },
        /* event frames for slave*/
/* ETF_MotorStates */
    {
        .frameType=LIN_FRAME_TYPE_EVENT,
        .id=58,
        .subIdIndex=2,
        .flagBitOffset=4,
        .bufLen=3,
        .signalList=LinConfig_0_Motor1State_Event_signals, /* reused unconditional frames signal*/
        .signalOffset=LinConfig_0_Motor1State_Event_offsets,
        .signalNum=2,
        .dir=LIN_DIR_PUBLISH,
        .checksumType=LIN_FRAME_CHECKSUM_ENHANCED,
        .idChangeable=l_true,
        .idRamIndex=4,                
    },
        /* diagnostic frames for slave*/
    {
        .frameType=LIN_FRAME_TYPE_DIAG,
        .id=0x3C,
        .flagBitOffset=5,
        .bufLen=8,
        .dir=LIN_DIR_SUBSCRIBE,
        .checksumType=LIN_FRAME_CHECKSUM_CLASSIC,
        .idChangeable=l_false,
    },
    {
        .frameType=LIN_FRAME_TYPE_DIAG,
        .id=0x3D,
        .flagBitOffset=6,
        .bufLen=8,
        .dir=LIN_DIR_PUBLISH,
        .checksumType=LIN_FRAME_CHECKSUM_CLASSIC,
        .idChangeable=l_false,
    },
    };


static l_u8 LinConfig_0_frame_flag_pool[1];





static const lin_slave_config_t LinConfig_0_slave_config={
    .initNad=2,
    .configNad=2,
    .product_id={
        .supplierId=30,
        .functionId=1,
        .variant=0,
    },
    .sn={
        .serial0=0,
        .serial1=0,
        .serial2=0,
        .serial3=0,
    },
    .p2Min=100,
    .stMin=20,
    .nAsTimeout=1000,
    .nCrTimeout=1000,
};





static lin_slave_state_t LinConfig_0_slave_state;


static lin_tp_state_t LinConfig_0_tp_state;
static lin_tp_item_t LinConfig_0_tp_tx_queue[30];
static lin_tp_item_t LinConfig_0_tp_rx_queue[30];
static l_u8 LinConfig_0_id_list[6]={
    2,
     0xAD,
     0x73,
     0x76,
     0xBA,
     0xF5,
};
static const lin_tp_config_t LinConfig_0_tp_config={
    .txQueueSize=30,
    .rxQueueSize=30,
    .rxMaxFrameLength=179,
    .idList=LinConfig_0_id_list,
    .idNum=6,
};

const lin_config_t LinConfig_0_config={
    .hwInst=0,
    .nodeType=LIN_NODE_SLAVE,
    .frameNum=7,
    .frameList=LinConfig_0_frames,
    .signalList=LinConfig_0_signals,
    .signalNum=9,
    .masterCfg=NULL,
    .slaveCfg=&LinConfig_0_slave_config,
    .tpCfg=&LinConfig_0_tp_config,
    .baudRate=19200,
    .maxIdleTimeoutMs=4000,
    .coreErrorCb=NULL,
};



/* =====  end of LinConfig_0 config ==== */





const lin_config_t* g_linGlobalConfig[LIN_IFC_INST]={
    &LinConfig_0_config,
};


lin_core_state_t g_linGlobalState[LIN_IFC_INST];



void lin_config_init(void)
{
    /* LinConfig_0*/
    g_linGlobalState[0].dataPool = LinConfig_0_data_pool;
    g_linGlobalState[0].signalFlagPool = LinConfig_0_flag_pool;
    g_linGlobalState[0].frameFlagPool = LinConfig_0_frame_flag_pool;
    g_linGlobalState[0].updateFlagPool = LinConfig_0_update_pool;
    g_linGlobalState[0].slaveState = &LinConfig_0_slave_state;
    g_linGlobalState[0].tpState = &LinConfig_0_tp_state;
    LinConfig_0_tp_state.rxQueue.items=LinConfig_0_tp_rx_queue;
    LinConfig_0_tp_state.txQueue.items=LinConfig_0_tp_tx_queue;
    
}