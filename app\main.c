/* USER CODE BEGIN Header */
/* you can remove the copyright */

/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 *
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 *
 * @file main.c
 * @brief
 *
 */

/* USER CODE END Header */
#include "sdk_project_config.h"
/* Includes ------------------------------------------------------------------*/

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "uds.h"
#include "lin_core.h"
#include "lin_tp.h"
#include "boot_jump.h"
#include "Uds_Service.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */
/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
#define LPTMRConf_Channel_0             (0U)
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */
/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN PV */
const uint32_t key[4] = {0x2b7e1516, 0x28aed2a6, 0xabf71588, 0x09cf4f3c};
/* USER CODE END PV */

/* Private function declare --------------------------------------------------*/
/* USER CODE BEGIN PFDC */
void lpTMR0_IRQHandler(void);
/* USER CODE END PFDC */
static void Board_Init(void);

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
l_u8 ld_read_by_id_callout (l_ifc_handle inst,l_u8 id,l_u8* data){
    return LD_NEGATIVE_RESPONSE;
}
/* USER CODE END 0 */


/**
 * @brief  The application entry point.
 * @retval int
 */
int main(void)
{
    /* USER CODE BEGIN 1 */
    RCU->RSSR = 0x1;
    /* USER CODE END 1 */ 
    Board_Init();
    /* USER CODE BEGIN 2 */
    l_sys_init();
    l_ifc_init(0);
    ld_init(0);
    Uds_Init();
    StayInBoot_Init();    
    INT_SYS_EnableIRQ(lpTMR0_IRQn);
    lpTMR_DRV_StartCounter(LPTMRConf_Channel_0);
    pTMR_DRV_StartTimerChannels(0,0);
    /* USER CODE END 2 */

    /* Infinite loop */
    /* USER CODE BEGIN WHILE */
    while (1)
    {
        Uds_MainFunction();
        StayInBoot_Task();
        /* USER CODE END WHILE */
        /* USER CODE BEGIN 3 */
        // Boot_MainFunction();
    }
    /* USER CODE END 3 */
}

static void Board_Init(void)
{
    CLOCK_SYS_Init(g_clockManConfigsArr,CLOCK_MANAGER_CONFIG_CNT,g_clockManCallbacksArr,CLOCK_MANAGER_CALLBACK_CNT);
    CLOCK_SYS_UpdateConfiguration(CLOCK_MANAGER_ACTIVE_INDEX,CLOCK_MANAGER_POLICY_AGREEMENT);
    PINS_DRV_Init(NUM_OF_CONFIGURED_PINS0,g_pin_mux_InitConfigArr0);
    lpTMR_DRV_Init(0,&LPTMR_Config,false);
    LINFlexD_DRV_Init(0,&linflexd_lin_config0,&linflexd_lin_config0_State);
    TRNG_DRV_Init(TRNG_INST,TRNG_ENTROPY_DELAY);
    pTMR_DRV_Init(0,&PTMR_Config);
    FLASH_DRV_Init(0,&flash_config0,&flash_config0_State);
    INT_SYS_ConfigInit();
}

/* USER CODE BEGIN 4 */
volatile uint32_t ECU_m_num_SysTick = 0;
volatile uint32_t DCM_m_t_PollingTimer = 0;
volatile uint32_t DCM_m_t_StayInTimer = 0;
void lpTMR0_IRQHandler(void)
{
    Uds_TimeService(1);
    lpTMR_DRV_ClearCompareFlag(0);
    // Boot_TimeService();
    ECU_m_num_SysTick++;
    if (ECU_m_num_SysTick % 100 == 0)
    {
        PINS_DRV_TogglePins(GPIOB, 1 << 4);
    }
}

void pTMR0_IRQHandler()
{
    pTMR_DRV_ClearInterruptFlagTimerChannels(0,0);
    lin_timeout_handle(0);
}
/* USER CODE END 4 */
