[{"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\clock_config.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\clock_config.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\clock_config.c", "output": "CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\clock_config.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\pin_mux.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\pin_mux.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\pin_mux.c", "output": "CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\pin_mux.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\lin_lib_config.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\lin_lib_config.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\lin_lib_config.c", "output": "CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\lin_lib_config.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\startup.S.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\startup.S", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\startup.S", "output": "CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\startup.S.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\vector.S.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\vector.S", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\vector.S", "output": "CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\vector.S.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\vector_table_copy.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\vector_table_copy.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\vector_table_copy.c", "output": "CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\vector_table_copy.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\RamInit0.S.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\RamInit0.S", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\RamInit0.S", "output": "CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\RamInit0.S.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\RamInit1.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\RamInit1.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\RamInit1.c", "output": "CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\RamInit1.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\RamInit2.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\RamInit2.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\RamInit2.c", "output": "CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\RamInit2.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\interrupt_config.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\interrupt_config.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\interrupt_config.c", "output": "CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\interrupt_config.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\dma_config.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\dma_config.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\dma_config.c", "output": "CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\dma_config.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\linflexd_lin_config.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\linflexd_lin_config.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\linflexd_lin_config.c", "output": "CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\linflexd_lin_config.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\ptmr_config.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\ptmr_config.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\ptmr_config.c", "output": "CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\ptmr_config.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\lptmr_config.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\lptmr_config.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\lptmr_config.c", "output": "CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\lptmr_config.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\crc_config.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\crc_config.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\crc_config.c", "output": "CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\crc_config.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\hcu_config.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\hcu_config.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\hcu_config.c", "output": "CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\hcu_config.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\flash_config.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\flash_config.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\flash_config.c", "output": "CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\flash_config.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\uds_config.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\uds_config.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\board\\uds_config.c", "output": "CMakeFiles\\GENERATED_CONFIG_TARGET.dir\\board\\uds_config.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\clock\\YTM32B1Mx\\clock_YTM32B1Mx.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\clock\\YTM32B1Mx\\clock_YTM32B1Mx.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\clock\\YTM32B1Mx\\clock_YTM32B1Mx.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\clock\\YTM32B1Mx\\clock_YTM32B1Mx.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\pins\\pins_driver.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\pins\\pins_driver.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\pins\\pins_driver.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\pins\\pins_driver.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\pins\\pins_port_hw_access.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\pins\\pins_port_hw_access.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\pins\\pins_port_hw_access.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\pins\\pins_port_hw_access.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\interrupt\\interrupt_manager.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\interrupt\\interrupt_manager.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\interrupt\\interrupt_manager.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\interrupt\\interrupt_manager.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\dma\\dma_driver.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\dma\\dma_driver.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\dma\\dma_driver.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\dma\\dma_driver.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\dma\\dma_hw_access.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\dma\\dma_hw_access.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\dma\\dma_hw_access.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\dma\\dma_hw_access.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\dma\\dma_irq.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\dma\\dma_irq.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\dma\\dma_irq.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\dma\\dma_irq.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\linflexd\\linflexd_lin_driver.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\linflexd\\linflexd_lin_driver.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\linflexd\\linflexd_lin_driver.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\linflexd\\linflexd_lin_driver.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\linflexd\\linflexd_lin_irq.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\linflexd\\linflexd_lin_irq.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\linflexd\\linflexd_lin_irq.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\linflexd\\linflexd_lin_irq.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\ptmr\\ptmr_driver.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\ptmr\\ptmr_driver.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\ptmr\\ptmr_driver.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\ptmr\\ptmr_driver.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\lptmr\\lptmr_driver.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\lptmr\\lptmr_driver.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\lptmr\\lptmr_driver.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\lptmr\\lptmr_driver.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\lptmr\\lptmr_hw_access.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\lptmr\\lptmr_hw_access.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\lptmr\\lptmr_hw_access.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\lptmr\\lptmr_hw_access.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\crc\\crc_driver.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\crc\\crc_driver.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\crc\\crc_driver.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\crc\\crc_driver.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\crc\\crc_hw_access.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\crc\\crc_hw_access.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\crc\\crc_hw_access.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\crc\\crc_hw_access.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\hcu\\hcu_driver.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\hcu\\hcu_driver.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\hcu\\hcu_driver.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\hcu\\hcu_driver.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\hcu\\hcu_irq.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\hcu\\hcu_irq.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\hcu\\hcu_irq.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\hcu\\hcu_irq.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\trng\\trng_driver.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\trng\\trng_driver.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\trng\\trng_driver.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\trng\\trng_driver.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\trng\\trng_hw_access.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\trng\\trng_hw_access.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\trng\\trng_hw_access.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\trng\\trng_hw_access.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\flash\\flash_driver.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\flash\\flash_driver.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\drivers\\src\\flash\\flash_driver.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\drivers\\src\\flash\\flash_driver.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\devices\\YTM32B1MD1\\startup\\system_YTM32B1MD1.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\devices\\YTM32B1MD1\\startup\\system_YTM32B1MD1.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\platform\\devices\\YTM32B1MD1\\startup\\system_YTM32B1MD1.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\platform\\devices\\YTM32B1MD1\\startup\\system_YTM32B1MD1.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\middleware\\lin_stack\\src\\lin_core.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\middleware\\lin_stack\\src\\lin_core.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\middleware\\lin_stack\\src\\lin_core.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\middleware\\lin_stack\\src\\lin_core.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\middleware\\lin_stack\\src\\lin_tp.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\middleware\\lin_stack\\src\\lin_tp.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\middleware\\lin_stack\\src\\lin_tp.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\middleware\\lin_stack\\src\\lin_tp.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\middleware\\uds\\src\\uds_ip.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\middleware\\uds\\src\\uds_ip.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\middleware\\uds\\src\\uds_ip.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\middleware\\uds\\src\\uds_ip.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\middleware\\uds\\src\\uds.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\middleware\\uds\\src\\uds.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\middleware\\uds\\src\\uds.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\middleware\\uds\\src\\uds.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\GENERATED_SDK_TARGET.dir\\rtos\\osif\\osif_baremetal.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\rtos\\osif\\osif_baremetal.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\rtos\\osif\\osif_baremetal.c", "output": "CMakeFiles\\GENERATED_SDK_TARGET.dir\\rtos\\osif\\osif_baremetal.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/app -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\uds_lin_bootloader.elf.dir\\app\\main.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\app\\main.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\app\\main.c", "output": "CMakeFiles\\uds_lin_bootloader.elf.dir\\app\\main.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/app -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\uds_lin_bootloader.elf.dir\\app\\GenerateKeyExImpl.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\app\\GenerateKeyExImpl.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\app\\GenerateKeyExImpl.c", "output": "CMakeFiles\\uds_lin_bootloader.elf.dir\\app\\GenerateKeyExImpl.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/app -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\uds_lin_bootloader.elf.dir\\app\\Uds_Service.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\app\\Uds_Service.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\app\\Uds_Service.c", "output": "CMakeFiles\\uds_lin_bootloader.elf.dir\\app\\Uds_Service.c.o"}, {"directory": "D:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/build", "command": "C:\\Users\\<USER>\\AppData\\Roaming\\yt_config_tool\\gcc-arm-none-eabi-10.3-2021.10\\bin\\arm-none-eabi-gcc.exe -DARMCM33_DSP_FP -DCORTEXM -DCPU_YTM32B1MD1 -DYTM32B1MD1 -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/app -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/board -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/clock/YTM32B1Mx -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/pins -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/dma -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/linflexd -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/ptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/lptmr -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/crc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/hcu -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/trng -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/src/flash -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/drivers/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/common -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/platform/devices/YTM32B1MD1/startup -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/lin_stack/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/middleware/uds/inc -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/CMSIS/Core/Include -ID:/project/uds_lin_fbl_md14_release_20250509/uds_lin_bootloader/rtos/osif -mcpu=cortex-m33 -g -mlittle-endian -mthumb -specs=nosys.specs -Wall -Werror=all -Wno-error=comment -g -O1 -ffunction-sections -fdata-sections -Wno-error=unused-variable -Wno-error=unused-parameter -Wno-error=unused-function -Wno-error=unused-but-set-variable -Wno-error=unused-result -Wno-error=maybe-uninitialized -Wno-error=sign-compare -Wno-error=strict-aliasing -Wno-error=unknown-pragmas -Wno-error=format -fdiagnostics-color=always -o CMakeFiles\\uds_lin_bootloader.elf.dir\\app\\boot_jump.c.o -c D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\app\\boot_jump.c", "file": "D:\\project\\uds_lin_fbl_md14_release_20250509\\uds_lin_bootloader\\app\\boot_jump.c", "output": "CMakeFiles\\uds_lin_bootloader.elf.dir\\app\\boot_jump.c.o"}]