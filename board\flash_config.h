/*
 *  Copyright 2020-2024 Yuntu Microelectronics co.,ltd
 *  All rights reserved.
 * 
 *  YUNTU Confidential. This software is owned or controlled by YUNTU and may only be
 *  used strictly in accordance with the applicable license terms. By expressly
 *  accepting such terms or by downloading, installing, activating and/or otherwise
 *  using the software, you are agreeing that you have read, and that you agree to
 *  comply with and are bound by, such license terms. If you do not agree to be
 *  bound by the applicable license terms, then you may not retain, install,
 *  activate or otherwise use the software. The production use license in
 *  Section 2.3 is expressly granted for this software.
 * 
 * @file flash_config.h
 * @brief 
 * 
 */




#ifndef __FLASH_CONFIG_H__
#define __FLASH_CONFIG_H__




#include "flash_driver.h"




/*flash_config0*/
extern flash_state_t flash_config0_State;
extern flash_user_config_t flash_config0;

#endif

