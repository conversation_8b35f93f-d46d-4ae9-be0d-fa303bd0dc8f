
LIN_description_file;
LIN_protocol_version = "2.2";
LIN_language_version = "2.2";
LIN_speed = 19.2 kbps;



Nodes {
  Master: SeatECU, 5 ms, 0.1 ms ;
  Slaves: Motor1 ;
}

Signals {
  Motor1_Dynamic_Sig: 8, 7, Motor1, SeatECU ;
  Motor1ErrorCode: 8, 5, Motor1, SeatECU ;
  Motor1ErrorValue: 8, 1, Motor1, SeatECU ;
  Motor1LinError: 1, 0, Motor1, SeatECU ;
  Motor1Position: 32, { 0, 0, 0, 0 }, Motor1, SeatECU ;
  Motor1Temp: 8, 5, Motor1, SeatECU ;
  MotorDirection: 2, 0, SeatECU, Motor1 ;
  MotorSelection: 4, 0, SeatECU, Motor1 ;
  MotorSpeed: 10, 0, SeatECU, Motor1 ;
}


Diagnostic_signals {
  MasterReqB0: 8, 0 ;
  MasterReqB1: 8, 0 ;
  MasterReqB2: 8, 0 ;
  MasterReqB3: 8, 0 ;
  MasterReqB4: 8, 0 ;
  MasterReqB5: 8, 0 ;
  MasterReqB6: 8, 0 ;
  MasterReqB7: 8, 0 ;
  SlaveRespB0: 8, 0 ;
  SlaveRespB1: 8, 0 ;
  SlaveRespB2: 8, 0 ;
  SlaveRespB3: 8, 0 ;
  SlaveRespB4: 8, 0 ;
  SlaveRespB5: 8, 0 ;
  SlaveRespB6: 8, 0 ;
  SlaveRespB7: 8, 0 ;
}



Frames {
  Motor1_Dynamic: 53, Motor1, 1 {
    Motor1_Dynamic_Sig, 0 ;
  }
  Motor1State_Cycl: 51, Motor1, 7 {
    Motor1Temp, 0 ;
    Motor1Position, 8 ;
    Motor1LinError, 40 ;
    Motor1_Dynamic_Sig, 41 ;
  }
  Motor1State_Event: 54, Motor1, 3 {
    Motor1ErrorCode, 8 ;
    Motor1ErrorValue, 16 ;
  }
  MotorControl: 45, SeatECU, 2 {
    MotorDirection, 0 ;
    MotorSpeed, 2 ;
    MotorSelection, 12 ;
  }
}

Sporadic_frames {
  SporadicPollingFrame: MotorControl ;
}

Event_triggered_frames {
  ETF_MotorStates: ETF_CollisionResolving, 58, Motor1State_Event ;
}

Diagnostic_frames {
  MasterReq: 0x3c {
    MasterReqB0, 0 ;
    MasterReqB1, 8 ;
    MasterReqB2, 16 ;
    MasterReqB3, 24 ;
    MasterReqB4, 32 ;
    MasterReqB5, 40 ;
    MasterReqB6, 48 ;
    MasterReqB7, 56 ;
  }
  SlaveResp: 0x3d {
    SlaveRespB0, 0 ;
    SlaveRespB1, 8 ;
    SlaveRespB2, 16 ;
    SlaveRespB3, 24 ;
    SlaveRespB4, 32 ;
    SlaveRespB5, 40 ;
    SlaveRespB6, 48 ;
    SlaveRespB7, 56 ;
  }
}

Node_attributes {
  Motor1{
    LIN_protocol = "2.2" ;
    configured_NAD = 2 ;
    initial_NAD = 2 ;
    product_id = 30, 1, 0 ;
    response_error = Motor1LinError ;
    P2_min = 100 ms ;
    ST_min = 20 ms ;
    N_As_timeout = 1000 ms ;
    N_Cr_timeout = 1000 ms ;
    configurable_frames {
      MotorControl ;
      Motor1State_Cycl ;
      Motor1State_Event ;
      ETF_MotorStates ;
      Motor1_Dynamic ;
    }
  }
}

Schedule_tables {
  NormalTable {
    MotorControl delay 50 ms ;
    Motor1State_Cycl delay 50 ms ;
  }
  DynamicTable {
    Motor1_Dynamic delay 100 ms ;
  }
  NormalTableSporadic {
    Motor1State_Cycl delay 50 ms ;
    SporadicPollingFrame delay 50 ms ;
  }
  NormalTableEvent {
    MotorControl delay 50 ms ;
    Motor1State_Cycl delay 50 ms ;
    ETF_MotorStates delay 50 ms ;
  }
  ETF_CollisionResolving {
    Motor1State_Event delay 10 ms ;
  }
  MasterReqTable {
    MasterReq delay 10 ms ;
  }
  SlaveRespTable {
    SlaveResp delay 10 ms ;
  }
}


Signal_encoding_types {
  MotorSpeed {
    physical_value, 0, 10, 1, 0, "rpm" ; 
  }
  encTemperature {
    physical_value, 0, 80, 0.5, -20, "Degree" ; 
  }
}

Signal_representation {
  MotorSpeed: MotorSpeed ;
  encTemperature: Motor1Temp, Motor2Temp ;
}
