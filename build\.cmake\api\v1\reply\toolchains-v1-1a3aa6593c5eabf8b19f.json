{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {}, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc.exe", "version": ""}, "language": "ASM", "sourceFileExtensions": ["s", "S", "asm"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include", "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include-fixed", "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include"], "linkDirectories": ["C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp", "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib/thumb/v8-m.main/nofp", "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1", "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc", "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["stdc++", "m", "gcc", "c", "gcc", "c", "nosys"]}, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-gcc.exe", "version": "10.3.1"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include/c++/10.3.1", "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include/c++/10.3.1/arm-none-eabi/thumb/v8-m.main/nofp", "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include/c++/10.3.1/backward", "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include", "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/include-fixed", "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/include"], "linkDirectories": ["C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main/nofp", "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib/thumb/v8-m.main/nofp", "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc/arm-none-eabi/10.3.1", "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/lib/gcc", "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/arm-none-eabi/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["stdc++", "m", "gcc", "c", "gcc", "c", "nosys"]}, "path": "C:/Users/<USER>/AppData/Roaming/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10/bin/arm-none-eabi-g++.exe", "version": "10.3.1"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "mm", "mpp", "CPP", "ixx", "cppm"]}], "version": {"major": 1, "minor": 0}}