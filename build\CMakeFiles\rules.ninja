# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.26

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: uds_lin_bootloader
# Configurations: Debug
# =============================================================================
# =============================================================================

#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\Users\<USER>\AppData\Roaming\yt_config_tool\gcc-arm-none-eabi-10.3-2021.10\bin\arm-none-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER__GENERATED_CONFIG_TARGET_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\Users\<USER>\AppData\Roaming\yt_config_tool\gcc-arm-none-eabi-10.3-2021.10\bin\arm-none-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__GENERATED_CONFIG_TARGET_Debug
  command = cmd.exe /C "$PRE_LINK && C:\Users\<USER>\AppData\Roaming\yt_config_tool\gcc-arm-none-eabi-10.3-2021.10\bin\arm-none-eabi-gcc-ar.exe -crs $TARGET_FILE $LINK_FLAGS $in && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__GENERATED_SDK_TARGET_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\Users\<USER>\AppData\Roaming\yt_config_tool\gcc-arm-none-eabi-10.3-2021.10\bin\arm-none-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__GENERATED_SDK_TARGET_Debug
  command = cmd.exe /C "$PRE_LINK && C:\Users\<USER>\AppData\Roaming\yt_config_tool\gcc-arm-none-eabi-10.3-2021.10\bin\arm-none-eabi-gcc-ar.exe -crs $TARGET_FILE $LINK_FLAGS $in && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__uds_lin_bootloader.2eelf_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\Users\<USER>\AppData\Roaming\yt_config_tool\gcc-arm-none-eabi-10.3-2021.10\bin\arm-none-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C executable.

rule C_EXECUTABLE_LINKER__uds_lin_bootloader.2eelf_Debug
  command = cmd.exe /C "$PRE_LINK && C:\Users\<USER>\AppData\Roaming\yt_config_tool\gcc-arm-none-eabi-10.3-2021.10\bin\arm-none-eabi-g++.exe $LINK_FLAGS -o $TARGET_FILE $in $LINK_PATH $LINK_LIBRARIES && $POST_BUILD"
  description = Linking C executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = C:\Users\<USER>\AppData\Roaming\yt_config_tool\cmake-3.26.4-windows-x86_64\bin\cmake.exe --regenerate-during-build -SD:\project\uds_lin_fbl_md14_release_20250509\uds_lin_bootloader -BD:\project\uds_lin_fbl_md14_release_20250509\uds_lin_bootloader\build
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = C:\Windows\System32\ninja.exe $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = C:\Windows\System32\ninja.exe -t targets
  description = All primary targets available:

